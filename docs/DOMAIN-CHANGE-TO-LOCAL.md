# Domain Change Script: pinkangel.bg → pinkangel.local

This guide explains how to use the `change-domain-to-local.sh` script to change WordPress URLs from the production domain (`pinkangel.bg`) to the local development domain (`pinkangel.local`) after importing a production database.

## 🎯 Purpose

This script is specifically designed for the scenario where you:
1. Download a production database from `pinkangel.bg`
2. Import it into your local development environment
3. Need to change all URLs to work with the local reverse proxy setup (`pinkangel.local`)

## 🚀 Quick Start

### Prerequisites

1. **WordPress environment running**:
   ```bash
   make dev    # For development environment
   # OR
   make prod   # For production environment
   ```

2. **Add pinkangel.local to /etc/hosts** (recommended):
   ```bash
   echo "127.0.0.1 pinkangel.local" | sudo tee -a /etc/hosts
   ```

3. **Production database imported** with URLs containing `pinkangel.bg`

### Basic Usage

```bash
# Auto-detect environment and change URLs
./scripts/change-domain-to-local.sh

# Specify environment explicitly
./scripts/change-domain-to-local.sh dev
./scripts/change-domain-to-local.sh prod
```

## 📋 What the Script Does

### 1. Environment Detection
- Automatically detects if development or production environment is running
- Validates that the WordPress container is accessible

### 2. Prerequisites Check
- Checks if `/etc/hosts` contains `pinkangel.local` entry
- Warns if reverse proxy is not running (for production environment)
- Allows you to continue even if prerequisites are not met

### 3. URL Analysis
- Shows current WordPress site and home URLs
- Performs dry-run analysis to show what will be changed
- Counts replacements needed for:
  - HTTPS URLs (`https://pinkangel.bg` → `https://pinkangel.local`)
  - HTTP URLs (`http://pinkangel.bg` → `https://pinkangel.local`)
  - Domain-only references (`pinkangel.bg` → `pinkangel.local`)

### 4. URL Changes
- Updates WordPress core options (`siteurl` and `home`)
- Performs search-and-replace in all content (posts, pages, options, etc.)
- Asks for confirmation before applying changes

### 5. Cache Clearing
- Clears WordPress cache
- Clears Redis cache (if available)
- Clears object cache

### 6. Verification
- Shows final URLs to confirm changes were applied
- Provides next steps for testing

## 🔧 Example Output

```bash
🌐 WordPress Domain Changer: pinkangel.bg → pinkangel.local
============================================================

[INFO] Detected environment: dev

[STEP] Checking prerequisites...
[SUCCESS] /etc/hosts contains pinkangel.local entry

[STEP] Changing URLs in development environment...
[INFO] Container: wordpress-dev
[INFO] Source URL: https://pinkangel.bg
[INFO] Target URL: https://pinkangel.local

[INFO] Current URLs in database:
  Site URL: https://pinkangel.bg
  Home URL: https://pinkangel.bg

[STEP] Updating WordPress core URLs...
[SUCCESS] Core URLs updated

[STEP] Analyzing URL replacements needed...
[INFO] HTTPS URLs to replace: 15
[INFO] HTTP URLs to replace: 3
[INFO] Domain-only references to replace: 8
[INFO] Total replacements needed: 26

Apply these URL replacements? (y/N): y

[STEP] Applying URL replacements...
[SUCCESS] URL replacements completed!

[STEP] Clearing caches...
[SUCCESS] Cache clearing completed

[STEP] Verifying URL changes...
  Site URL: https://pinkangel.local
  Home URL: https://pinkangel.local
[SUCCESS] URLs successfully changed to pinkangel.local!
```

## 🌐 Testing After URL Change

### Development Environment
1. **Direct access**: http://localhost:8080
2. **Via reverse proxy**: https://pinkangel.local (if proxy is running)
3. **Admin panel**: http://localhost:8080/wp-admin

### Production Environment
1. **Start reverse proxy**: `./scripts/manage-proxy.sh start`
2. **Test site**: https://pinkangel.local
3. **Admin panel**: https://pinkangel.local/wp-admin

## ⚠️ Important Notes

### SSL Certificates
- The script sets URLs to `https://pinkangel.local`
- You may see SSL warnings in your browser (self-signed certificates)
- This is normal for local development - accept the warnings

### Browser Cache
- Clear your browser cache after running the script
- Use Ctrl+Shift+R (or Cmd+Shift+R on Mac) for hard refresh

### Reverse Proxy
- For production environment, ensure the reverse proxy is running
- The script will warn you if it's not detected

## 🔄 Related Scripts

- `fix-urls-after-import.sh` - General URL fixing (localhost:8080 or pinkangel.bg)
- `manage-proxy.sh` - Manage the reverse proxy
- `sync-prod-to-dev.sh` - Sync production database to development

## 🆘 Troubleshooting

### "No WordPress environment detected"
```bash
# Start an environment first
make dev    # or make prod
```

### "Could not clear Redis cache"
- This is normal if Redis is not running
- The script continues without Redis cache clearing

### URLs not changing correctly
- Check that the database actually contains `pinkangel.bg` URLs
- Run the script again - it's safe to run multiple times
- Check WordPress admin → Settings → General for manual verification

### SSL/HTTPS issues
- Ensure `/etc/hosts` contains the pinkangel.local entry
- Start the reverse proxy for HTTPS access
- Accept browser SSL warnings for self-signed certificates
