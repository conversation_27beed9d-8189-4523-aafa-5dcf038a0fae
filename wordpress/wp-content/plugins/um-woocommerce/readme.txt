=== Ultimate Member - Woocommerce ===
Author URI: https://ultimatemember.com
Plugin URI: https://ultimatemember.com/extensions/woocommerce/
Contributors: nsinelnikov
Tags: woocommerce, products, user-profile, user-registration
Requires at least: 5.5
Tested up to: 6.7
Stable tag: 2.4.4
License: GNU Version 2 or Any Later Version
License URI: http://www.gnu.org/licenses/gpl-3.0.txt
Requires UM core at least: 2.8.7

Integrates the popular e-commerce plugin WooCommerce with Ultimate Member.

== Description ==

Integrates the popular e-commerce plugin WooCommerce with Ultimate Member.

= Key Features: =

* Integrates users purchases tab in user profile
* Integrates product reviews tab in user profile
* Display and view user orders from account page
* Manage shipping/billing address from account page
* Display total spent by user as profile field or on directory
* Display total orders by user as profile field or in directory
* Allow user to have a specific role if they purchase a specific product
* Allow user to have a specific role if they purchase any product
* Options to disable any of the profile or account tabs based on role
* WooCommerce Sequential Order Number add-on compatible
* WooCommerce Subscriptions add-on compatible

= Integrations with notifications extension: =

* Notifies a user when their account has been verified

= Integrations with Social Activity: =

* Shows activity when a user is verified

Read about all of the plugin's features at [Ultimate Member - Woocommerce](https://ultimatemember.com/extensions/woocommerce/)

= Documentation & Support =

Got a problem or need help with Ultimate Member? Head over to our [documentation](http://docs.ultimatemember.com/article/131-woocommerce-setup) and perform a search of the knowledge base. If you can’t find a solution to your issue then you can create a topic on the [support forum](https://wordpress.org/support/plugin/ultimate-member).

== Installation ==

1. Activate the plugin
2. That's it. Go to Ultimate Member > Settings > Extensions > Woocommerce to customize plugin options
3. For more details, please visit the official [Documentation](http://docs.ultimatemember.com/article/131-woocommerce-setup) page.

== Changelog ==

= 2.4.4: March 3, 2025 =

* Fixed: Total Orders filter on the member directory page.
* Fixed: Account page title.
* Fixed: markup in the "Billing Address" and "Shipping Address" account tabs.
* Fixed links in orders and subscriptions:
  - Removed the "View" link from the order "Order details" section.
  - Fixed the "View" link in the order "Related subscriptions" section.
  - Fixed the "View" link in the subscription "Related orders" section.
  - Fixed the "PDF Invoice" link in the subscription "Related orders" section.
  - Fixed the "Change address" link in the subscription Actions.
  - Fixed the "Renew now" link in the subscription Actions.

= 2.4.3: November 15, 2024 =

* Fixed: Billing/Shipping country/region fields
* Fixed: "Load textdomain just in time" issue

= 2.4.2: September 30, 2024 =

* Tweak: Compatibility with Ultimate Member 2.8.7
* Tweak: Compatibility account page with the future new UI

= 2.4.1: February 21, 2024 =

* Fixed: PHP fatal error in REST request
* Fixed: Integration with Member directory
* Tweak: Enhancements related to WPCS

= 2.4.0: January 15, 2024 =

* Added: Profile link to the Edit order screen
* Added: {um_profile_url} and {um_profile_link} placeholders for WooCommerce emails to display order's customer UM User Profile URL
* Fixed: Conditional checking if wp_query doesn't exist in restriction content handlers

= 2.3.9: December 20, 2023 =

* Fixed: Account page when Woocommerce version >= 8.4.0 is active

= 2.3.8: December 11, 2023 =

* Added: Support of the High-Performance Order Storage (HPOS) feature
* Fixed: Country field dropdowns in Account
* Fixed: Orders pagination styles in Account
* Fixed: The "Total Spent" filter range in the member directory
* Tweak: Using enqueue scripts suffix from UM core class. Dependency from UM core 2.7.0
* Tweak: `um-admin-clear` CSS class. It duplicates WordPress native `clear`. Using WordPress native instead
* Fixed: 'um_user_permissions_filter' hook attributes

= 2.3.7: October 11, 2023 =

* Updated: Dependencies versions based on the recent changes for `UM()->frontend()->enqueue()::get_suffix();`
* Fixed: Made redirect safe
* Fixed: Case when extension isn't active based on dependency, but we can provide the license key field

= 2.3.6: August 22, 2023 =

* Fixed: PHP error when Shop page isn't set
* Fixed: Product restriction inside Woocommerce Blocks. Force restriction by default
* Tweak: Unified `UM()->fields()->editing` and `UM()->fields()->viewing` to bool variables use true|false in conditions to make `===` or `!==` comparing

= 2.3.5: August 1, 2023 =

* Fixed: Product restriction inside Woocommerce Blocks
* Fixed: Shop page restrictions in case when shop page isn't enabled in Woocommerce settings

= 2.3.4: July 26, 2023 =

* Added: Restrict products in the loop
* Added: French translations (thanks to Shad Gagnon[<EMAIL>]);
* Tweak: Template overwrite versioning
* Tweak: Changed input_filter to WordPress native sanitize
* Fixed: PHP warnings
* Fixed: Change orders from new to old
* Fixed: `billing_email` and `billing_state` fields in account

= 2.3.3: December 14, 2022 =

* Added: Hook `um_woocommerce_account_orders_per_page` for pagination in the Account > Orders list
* Fixed: Conflict with the WooCommerce Memberships plugin that breaks dropdown fields on Account page

= 2.3.2: October 26, 2022 =

* Added: An order note when user role is changed. It's good for debugging role changes
* Added: A hook `um_woocommerce_after_member_role_upgrade`
* Fixed: PHP error on action `woocommerce_order_status_failed`
* Fixed: Unwanted user role change on subscription renewal order status change
* Fixed: Removing all roles on action `woocommerce_order_status_refunded`

= 2.3.1: August 17, 2022 =

* Added: WooCommerce custom callbacks to allowed callback options on install
* Added: Ultimate Member register page URL on the checkout page with required registration and logged-in user
* Fixed: Hide restriction products when using WooCommerce [products] shortcode
* Fixed: Account > Subscription auto-renew toggle, changed template

= 2.3.0: February 9, 2022 =

* Added: "Ignore" option in "Change role" dropdowns for the subscription products
* Fixed: PHP notice for the State field
* Fixed: Settings section key on wp-admin Settings screen
* Fixed: Compatibility with the latest Woocommerce: Subscriptions plugin and Subscriptions tab in the UM Account
* Fixed: AJAX template for subscriptions in the UM Account -> Subscription tab
* Fixed: Select2 and selectWoo JS libraries conflict. Partially via the hook `um_woocommerce_dequeue_selectWoo`

= 2.2.8: December 20, 2021 =

* Added: General setting for getting the "add to cart" button hidden
* Added: Arabic translation. Thanks to Nabil Moqbel (Slack: nabilmoqbel)
* Added: Member directory filters: WC Billing state, WC Shipping state
* Fixed: Avoid using caching when subscription status is changed and we need to change role
* Fixed: Wishlist RTL styles
* Fixed: Displaying order date in account
* Fixed: Enqueue WCFM script in account

* Templates required update:
  - orders.php

* Cached and optimized/minified assets(JS/CSS) must be flushed/re-generated after upgrade

= 2.2.7: August 3, 2021 =

* Tweak: rewritten Shop and products restriction settings

= 2.2.6: March 29, 2021 =

* Added: Billing city/Shipping city filter to member directory

= 2.2.5: December 16, 2020 =

* Fixed: Getting the first subscription payment method for roles assigning logic
* Fixed: "Change address" feature for subscriptions via the Account page
* Fixed: Subscription address change after update billing/shipping address
* Fixed: Duplicates assigning roles from processing to complete
* Fixed: Orders list's the next page button not visible in mobile view
* Fixed: Changed 'Billing Address' icon
* Fixed: State field in the Account page
* Fixed: Order view links in the Account page
* Fixed: Subscriptions list in the Account page

= 2.2.4: September 23, 2020 =

* Fixed role assigning logic for subscription products
* Fixed the "state" field loaded by AJAX
* Fixed role assigning when upgrade/downgrade the subscriptions
* Fixed removing roles on renewal order

= 2.2.3: August 11, 2020 =

* Added: Integration with YITH WooCommerce Wishlist plugin
* Changed: Assigning roles after payment complete or refund
* Changed: Assigning roles on subscription status changed
* Fixed: Styles for WooCommerce elements on the Account page
* Fixed: Billing/Shipping Country fields view
* Fixed: 'State' fields label and sorting
* Fixed: 'State' field value if default country is set up
* Tweak: apply_shortcodes() function support

= 2.2.2: April 1, 2020 =

* Fixed: Billing/Shipping Country fields and using options_pair
* Fixed: Account orders tab layout

= 2.2.1: March 09, 2020 =

* Changed: Subscription template
* Fixed: Billing/Shipping State field using a key instead of an option's label
* Fixed: Fields' options intersect when there are extra spaces and show them on members directory filters
* Fixed: "use_option_pairs" filter
* Fixed: Issue with reviews comments object

= 2.2.0: January 13, 2020 =

* Tweak: Integration with Ultimate Member 2.1.3 and UM metadata table
* Added: Cancel order to account page
* Added: Integration UM Account with plugin "WCFM - WooCommerce Multivendor Marketplace"
* Fixed: Existed metadata options in select-type filters
* Fixed: Saving the billing/shipping state address
* Fixed: Payment methods popup

= 2.1.9: November 11, 2019 =

* Tweak: Integration with Ultimate Member 2.1.0
* Added: Sanitize functions for request variables
* Added: esc_attr functions to avoid XSS vulnerabilities
* Added: "WooCommerce PDF Invoices" integration (PDF link to the Account orders table)
* Added: "Add payment method" tool to Account page
* Added: Billing & Shipping country fields filters
* Changed: View Order and View Subscription links URL


= 2.1.8: August 6, 2019 =

* Added: Escape functions
* Fixed: Account fields save
* Fixed: Billing/Shipping fields value

= 2.1.7: July 16, 2019 =

* Added: Memberships account tab
* Added: Templates
* Fixed: Profile Tabs
* Fixed: Uninstall Process
* Fixed: Billing/Shipping country fields values

= 2.1.6: May 1, 2019 =

* Added: My Payment Methods account tab
* Added: My Downloads account tab
* Fixed: Duplicate fields in Billing/Shipping account tabs
* Fixed: Date format in Account tabs

= 2.1.5: March 29, 2019 =

* Added: Predefined Billing/Shipping fields
* Fixed: Change Role assign after subscription status change

= 2.1.4: January 24, 2019 =

* Fixed: changed role notification

= 2.1.3: November 28, 2018 =

* Fixed: AJAX vulnerabilities
* Optimized: JS/CSS enqueue

= 2.1.2: November 15, 2018 =

* Fixed: JS on the subscription table

= 2.1.1: November 12, 2018 =

* Fixed: Date displaying in the Orders table;

= 2.1.0: October 14, 2018 =

* Optimized: JS/CSS enqueue

= 2.0.9: October 5, 2018 =

* Fixed: Multi Approves

= 2.0.8: October 3, 2018 =

* Fixed: New translate file
* Fixed: Change role on user canceling a subscription

= 2.0.7: September 28, 2018 =

* Fixed: WP native AJAX handlers

= 2.0.6: August 9, 2018 =

* Fixed: Templates include logic
* Fixed: View Subscription screen

= 2.0.5: June 29, 2018 =

* Fixed: Account Billing/Shipping Forms Validation

= 2.0.4: May 14, 2018 =

* Fixed: Save Account Forms

= 2.0.3: April 27, 2018 =

* Added: Loading translation from "wp-content/languages/plugins/" directory

= 2.0.2: April 15, 2018 =

* Fixed: add/change role on product purchase
* Fixed: change role on product refund

= 2.0.1: April 4, 2018 =

* Fixed: Subscriptions and Membership Area integrations
* Fixed: displaying of “Total Orders” and “Total Spent” in Member Directories
* Tweak: UM2.0 compatibility

= 1.0.11: February 20, 2017 =

* New: WooCommerce Subscription add-on compatibility
* New: WooCommerce Sequential Order Number add-on compatibility
* Tweaked: Update EDD Plugin updater library
* Fixed: Reset cache after completed purchase
* Fixed: Account fields security
* Fixed: Fatal errors in editing account page
* Fixed: Orders pagination in account page
* Fixed: Remove notices

= 1.0.10: June 9, 2016 =

* Added: new options to hide shipping and billing tabs
* Added: shortcode support
* Added: translatins for save address
* Added: options to hide purchases/reviews from other members
* Added: new action hooks 'um_woocommerce_orders_tab_before_table_header_row' & 'um_woocommerce_orders_tab_after_table_header_row'
* Fixed: Fix woocommerce billing text fields
* Fixed: buttons translation
* Fixed: license and updater
* Fixed: remove notices
* Fixed: saving billing and shipping form fields

= 1.0.9: February 1, 2016 =

* Fixed: Fix woocommerce billing text fields

= 1.0.8: January 29, 2016 =

* Tweak: Plugin updater updated to latest version
* Tweak: Remove notes
* Fixed: Add select2 to billing and shipping country and state

= 1.0.7: January 25, 2016 =

* Tweak: sync billing data from wc to um profile
* Tweak: sync um profile to woocommerce billing

= 1.0.6: December 11, 2015 =

* Tweak: compatibility with WP 4.4

= 1.0.5: December 8, 2015 =

* Initial release
