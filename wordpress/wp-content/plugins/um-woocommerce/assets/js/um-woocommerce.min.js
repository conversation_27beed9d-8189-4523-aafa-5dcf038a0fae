function um_wc_display_order(e,t){return void 0===t&&void 0!==e.currentTarget&&(e.preventDefault(e),t=jQuery(e.currentTarget).parents("tr").data("order_id"),e=jQuery(e.currentTarget).get(0),t||"string"!=typeof e.hash||(t=e.hash.replace(/\D/,"")),window.history.pushState("string","Orders",e.href)),wp.ajax.send("um_woocommerce_get_order",{data:{order_id:t,nonce:um_scripts.nonce},beforeSend:prepare_Modal,success:function(e){e?(show_Modal(e),responsive_Modal(),jQuery(".um-popup:visible").addClass("um-popup-order").show()):remove_Modal()},error:function(e){remove_Modal(),console.log("===UM Woocommerce error===",e)}})}function um_wc_display_subscription(e,t){var r;return void 0===t&&void 0!==e.currentTarget&&(e.preventDefault(e),t=("string"==typeof(r=jQuery(e.currentTarget).get(0)).hash?r.hash:(r=jQuery(e.currentTarget).closest("tr").find("td.subscription-id a")).text()).replace(/\D/,""),window.history.pushState("string","Subscriptions",r.href)),wp.ajax.send("um_woocommerce_get_subscription",{data:{subscription_id:t,nonce:um_scripts.nonce},beforeSend:function(){jQuery(".woocommerce_account_subscriptions").css({cursor:"wait",opacity:"0.7"})},success:function(e){"string"==typeof e.content&&(jQuery(".woocommerce_account_subscriptions").hide().after(e.content),jQuery(".um_account_subscription").fadeIn()),"object"==typeof e.script_params&&(WCSViewSubscription=e.script_params),"string"==typeof e.script_ready&&jQuery.globalEval(e.script_ready)},error:function(e){console.log("===UM Woocommerce error===",e)}})}function um_wc_refresh_address(e,t,r){var o,n,i,a=0,u=jQuery(r).closest("form"),c=jQuery(r).closest(".um-account-tab");c.length?c.find('input[name="form_id"]').length&&(a=c.find('input[name="form_id"]').val()):u.find('input[name="form_id"]').length?a=u.find('input[name="form_id"]').val():jQuery(r).closest(".um-field[id]").length&&"object"==typeof(c=jQuery(r).closest(".um-field[id]").attr("id").match(/um_field_(\d+)_(billing|shipping)_country/i))&&3===c.length&&(a=parseInt(c[1])),0<(o="billing_country"===t?u.find(".um-field.um-field-billing_state"):u.find(".um-field.um-field-shipping_state")).length&&(n=o.find(".um-field-error").clone(),i=o.find("label").text(),r=o.find("select,input").val(),wp.ajax.send("um_woocommerce_refresh_address",{data:{nonce:um_scripts.nonce,country:e,state:r,type:t,form_id:a},success:function(e){o.find(".um-field-error").length,o.html(jQuery(e).html()),0<o.find("select").length?o.find("select").each(function(e,t){t=jQuery(t);t.select2({dropdownParent:t.parent(),width:"100%"})}):0<o.find('input[type="hidden"]').length?o.hide():o.show(),n&&o.append(n),!a&&i&&o.find("label").text(i)},error:function(e){console.log("===UM Woocommerce error===",e)}}))}jQuery(document).ready(function(e){jQuery(".um-woo-review-avg").length&&jQuery(".um-woo-review-avg").um_raty({half:!0,starType:"i",number:function(){return jQuery(this).attr("data-number")},score:function(){return jQuery(this).attr("data-score")},hints:["1 Star","2 Star","3 Star","4 Star","5 Star"],space:!1,readOnly:!0});var t=jQuery(".um-field #billing_country"),r=jQuery(".um-field #shipping_country");t.length&&um_wc_refresh_address(t.val(),t.data("key"),t),r.length&&um_wc_refresh_address(r.val(),r.data("key"),r),jQuery(document.body).on("change",".um-field #billing_country, .um-field #shipping_country",function(){um_wc_refresh_address(jQuery(this).val(),jQuery(this).data("key"),jQuery(this))}),jQuery(".um-account-tab select.country_select, .um-account-tab select.state_select, .um-custom-shortcode-tab select").each(function(e,t){t=jQuery(t);t.select2({dropdownParent:t.parent(),width:"100%"})}),0<jQuery(".um-account-tab .um-field-state .um-field-error").length&&um_wc_refresh_address((t=jQuery(".um-account-tab .um-field-country:visible select")).val(),t.data("key"),t),jQuery(document.body).on("click",".um-woo-orders .my_account_orders .um-woo-view-order, .um-woo-orders .my_account_orders a.view",um_wc_display_order),jQuery(document.body).on("click",".um-woo-order-hide",function(e){return e.preventDefault(),window.history.pushState("string","Orders",window.location.pathname),remove_Modal(),!1}),-1<window.location.href.indexOf("/orders/")&&window.location.hash&&um_wc_display_order(e,window.location.hash.replace(/\D/,"")),jQuery(document.body).on("click",".um-woo-subscriptions .my_account_subscriptions a.view, .um-woo-subscriptions .my_account_subscriptions .subscription-id > a",um_wc_display_subscription),jQuery(document.body).on("click",".back_to_subscriptions",function(e){return e.preventDefault(),window.history.pushState("string","Subscriptions",window.location.pathname),jQuery(".woocommerce_account_subscriptions").removeAttr("style").fadeIn().nextAll(".um_account_subscription").remove(),!1}),-1<window.location.href.indexOf("/subscription/")&&window.location.hash&&um_wc_display_subscription(e,window.location.hash.replace(/\D/,"")),jQuery(document.body).on("click",'a[href*="add-payment-method"]',function(e){e.preventDefault(),/add-payment-method=1/.test(location.href)||(e=location.href+(/\?/.test(location.href)?"&":"?")+"add-payment-method=1",window.history.pushState("string","Add payment method",e)),jQuery("#um_add_payment_method_content .um-modal-overlay").remove(),jQuery("#um_add_payment_method_content .um-modal").removeClass("um-modal-hidden").show().after('<div class="um-modal-overlay"></div>'),jQuery(document.body).trigger("resize")}),jQuery("#um_add_payment_method_content").on("click",".um-modal-overlay",function(e){e.stopPropagation(),window.history.pushState("string","Orders",window.location.pathname);e=jQuery(e.currentTarget);e.siblings(".um-modal").addClass("um-modal-hidden").hide(),e.remove()}),/add-payment-method=1/.test(location.href)&&jQuery('a[href*="add-payment-method"]').length&&jQuery('a[href*="add-payment-method"]').trigger("click")});