/*
  Ultimate Member - Woocommerce dependencies
*/

const { src, dest, parallel } = require( 'gulp' );

const sass     = require( 'gulp-sass' )( require( 'sass' ) );
const uglify   = require( 'gulp-uglify' );
const cleanCSS = require( 'gulp-clean-css' );
const rename   = require( 'gulp-rename' );

function defaultTask( done ) {
	// sass.compiler = require( 'node-sass' );

	src( ['assets/js/v3/*.js','!assets/js/v3/*.min.js'] ) // path to your files
		.pipe( uglify() )
		.pipe( rename( { suffix: '.min' } ) )
		.pipe( dest( 'assets/js/v3/' ) );

	// full CSS files
	src( ['assets/css/v3/*.sass'] )
		.pipe( sass().on( 'error', sass.logError ) )
		.pipe( dest( 'assets/css/v3' ) );
	// min CSS files
	src( ['assets/css/v3/*.sass'] )
		.pipe( sass().on( 'error', sass.logError ) )
		.pipe( cleanCSS() )
		.pipe( rename( { suffix: '.min' } ) )
		.pipe( dest( 'assets/css/v3/' ) );


	src( ['assets/js/*.js','!assets/js/*.min.js'] ) // path to your files
		.pipe( uglify() )
		.pipe( rename( { suffix: '.min' } ) )
		.pipe( dest( 'assets/js/' ) );

	src(['assets/css/*.css', '!assets/css/*.min.css',])
		.pipe( cleanCSS() )
		.pipe( rename( { suffix: '.min' } ) )
		.pipe( dest( 'assets/css/' ) );


	done();
}
exports.default = defaultTask;
