<?php
namespace um_ext\um_woocommerce\core;

use Exception;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class WooCommerce_Main_API
 * @package um_ext\um_woocommerce\core
 */
class WooCommerce_Main_API {

	/**
	 * WooCommerce_Main_API constructor.
	 */
	public function __construct() {

		add_action( 'wp_ajax_um_woocommerce_get_order', array( $this, 'ajax_get_order' ) );
		add_action( 'wp_ajax_um_woocommerce_get_subscription', array( $this, 'ajax_get_subscription' ) );
		add_action( 'wp_ajax_um_woocommerce_refresh_address', array( $this, 'ajax_refresh_address' ) );
		add_action( 'wp_ajax_nopriv_um_woocommerce_refresh_address', array( $this, 'ajax_refresh_address' ) );

	}


	/**
	 * @param bool $keys_only
	 *
	 * @return array
	 */
	public function get_wc_address_fields( $keys_only = false ) {
		$fields = array();

		// billing
		$fields['billing_first_name'] = array(
			'title'    => __( 'WC Billing First name', 'um-woocommerce' ),
			'metakey'  => 'billing_first_name',
			'type'     => 'text',
			'label'    => __( 'WC Billing First name', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-user',
		);

		$fields['billing_last_name'] = array(
			'title'    => __( 'WC Billing Last name', 'um-woocommerce' ),
			'metakey'  => 'billing_last_name',
			'type'     => 'text',
			'label'    => __( 'WC Billing Last name', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-user',
		);

		$fields['billing_company'] = array(
			'title'    => __( 'WC Billing Company', 'um-woocommerce' ),
			'metakey'  => 'billing_company',
			'type'     => 'text',
			'label'    => __( 'WC Billing Company', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-user',
		);

		$fields['billing_address_1'] = array(
			'title'    => __( 'WC Billing Address 1', 'um-woocommerce' ),
			'metakey'  => 'billing_address_1',
			'type'     => 'text',
			'label'    => __( 'WC Billing Address 1', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-map-marker',
		);

		$fields['billing_address_2'] = array(
			'title'    => __( 'WC Billing Address 2', 'um-woocommerce' ),
			'metakey'  => 'billing_address_2',
			'type'     => 'text',
			'label'    => __( 'WC Billing Address 2', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-map-marker',
		);

		$fields['billing_city'] = array(
			'title'    => __( 'WC Billing city', 'um-woocommerce' ),
			'metakey'  => 'billing_city',
			'type'     => 'text',
			'label'    => __( 'WC Billing city', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-map-marker',
		);

		$fields['billing_postcode'] = array(
			'title'    => __( 'WC Billing postcode', 'um-woocommerce' ),
			'metakey'  => 'billing_postcode',
			'type'     => 'text',
			'label'    => __( 'WC Billing postcode', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-map-marker',
		);

		$fields['billing_country'] = array(
			'title'    => __( 'WC Billing country', 'um-woocommerce' ),
			'metakey'  => 'billing_country',
			'type'     => 'select',
			'label'    => __( 'WC Billing country', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-map-marker',
			'options'  => array_flip( UM()->builtin()->get( 'countries' ) ),
		);

		$fields['billing_state'] = array(
			'title'    => __( 'WC Billing state', 'um-woocommerce' ),
			'metakey'  => 'billing_state',
			'type'     => 'text',
			'label'    => __( 'WC Billing state', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-map-marker',
		);

		$fields['billing_phone'] = array(
			'title'    => __( 'WC Billing phone', 'um-woocommerce' ),
			'metakey'  => 'billing_phone',
			'type'     => 'text',
			'label'    => __( 'WC Billing phone', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-phone',
		);

		$fields['billing_email'] = array(
			'title'    => __( 'WC Billing email', 'um-woocommerce' ),
			'metakey'  => 'billing_email',
			'type'     => 'text',
			'label'    => __( 'WC Billing email', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-envelope',
		);

		// Shipping
		$fields['shipping_first_name'] = array(
			'title'    => __( 'WC Shipping First name', 'um-woocommerce' ),
			'metakey'  => 'shipping_first_name',
			'type'     => 'text',
			'label'    => __( 'WC Shipping First name', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-user',
		);

		$fields['shipping_last_name'] = array(
			'title'    => __( 'WC Shipping Last name', 'um-woocommerce' ),
			'metakey'  => 'shipping_last_name',
			'type'     => 'text',
			'label'    => __( 'WC Shipping Last name', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-user',
		);

		$fields['shipping_company'] = array(
			'title'    => __( 'WC Shipping Company', 'um-woocommerce' ),
			'metakey'  => 'shipping_company',
			'type'     => 'text',
			'label'    => __( 'WC Shipping Company', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-user',
		);

		$fields['shipping_address_1'] = array(
			'title'    => __( 'WC Shipping Address 1', 'um-woocommerce' ),
			'metakey'  => 'shipping_address_1',
			'type'     => 'text',
			'label'    => __( 'WC Shipping Address 1', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-map-marker',
		);

		$fields['shipping_address_2'] = array(
			'title'    => __( 'WC Shipping Address 2', 'um-woocommerce' ),
			'metakey'  => 'shipping_address_2',
			'type'     => 'text',
			'label'    => __( 'WC Shipping Address 2', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-map-marker',
		);

		$fields['shipping_city'] = array(
			'title'    => __( 'WC Shipping city', 'um-woocommerce' ),
			'metakey'  => 'shipping_city',
			'type'     => 'text',
			'label'    => __( 'WC Shipping city', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-map-marker',
		);

		$fields['shipping_postcode'] = array(
			'title'    => __( 'WC Shipping postcode', 'um-woocommerce' ),
			'metakey'  => 'shipping_postcode',
			'type'     => 'text',
			'label'    => __( 'WC Shipping postcode', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-map-marker',
		);

		$fields['shipping_country'] = array(
			'title'    => __( 'WC Shipping country', 'um-woocommerce' ),
			'metakey'  => 'shipping_country',
			'type'     => 'select',
			'label'    => __( 'WC Shipping country', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-map-marker',
			'options'  => array_flip( UM()->builtin()->get( 'countries' ) ),
		);

		$fields['shipping_state'] = array(
			'title'    => __( 'WC Shipping state', 'um-woocommerce' ),
			'metakey'  => 'shipping_state',
			'type'     => 'text',
			'label'    => __( 'WC Shipping state', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-map-marker',
		);

		$fields['shipping_phone'] = array(
			'title'    => __( 'WC Shipping phone', 'um-woocommerce' ),
			'metakey'  => 'shipping_phone',
			'type'     => 'text',
			'label'    => __( 'WC Shipping phone', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-phone',
		);

		$fields['shipping_email'] = array(
			'title'    => __( 'WC Shipping email', 'um-woocommerce' ),
			'metakey'  => 'shipping_email',
			'type'     => 'text',
			'label'    => __( 'WC Shipping email', 'um-woocommerce' ),
			'public'   => true,
			'editable' => true,
			'icon'     => 'um-faicon-envelope',
		);

		if ( $keys_only ) {
			return array_keys( $fields );
		}

		return $fields;
	}

	/**
	 * Check if Woo Subscriptions plugin is active
	 *
	 * @return bool
	 */
	public function is_wc_subscription_plugin_active() {
		return function_exists( 'wcs_get_subscription' );
	}

	/**
	 * @param int $user_id
	 *
	 * @return bool
	 */
	public function maybe_skip_user_by_role( $user_id ) {
		$skip = false;

		$excludes = UM()->options()->get( 'woo_oncomplete_except_roles' );
		$excludes = empty( $excludes ) ? array() : $excludes;

		foreach ( $excludes as $role ) {
			if ( user_can( $user_id, $role ) ) {
				$skip = true;
				break;
			}
		}

		return $skip;
	}

	/**
	 * @param $user_id
	 */
	public function maybe_auto_approve( $user_id ) {
		um_fetch_user( $user_id );

		$auto_approve = apply_filters( 'um_woocommerce_auto_approve_on_completed', true );
		if ( $auto_approve ) {
			UM()->common()->users()->approve( $user_id, true );
		}

		um_reset_user();
	}

	/**
	 * Check single product order need or not need to change user role
	 *
	 * @param int $order_id
	 *
	 * @return array|bool
	 *
	 * @throws Exception
	 */
	public function change_role_data_single( $order_id ) {
		$order   = new \WC_Order( $order_id );
		$user_id = $order->get_user_id();

		$skip_by_role = $this->maybe_skip_user_by_role( $user_id );
		if ( $skip_by_role ) {
			return false;
		}

		$return = false;
		$data   = array();
		//items have more priority
		$items = $order->get_items();
		foreach ( $items as $item_key => $item ) {
			if ( $this->is_wc_subscription_plugin_active() ) {
				$is_subscription = \WC_Subscriptions_Product::is_subscription( $item['product_id'] );

				if ( $is_subscription ) {
					$return = true;
					continue;
				}
			}

			$role_item = get_post_meta( $item['product_id'], '_um_woo_product_role', true );
			if ( ! empty( $role_item ) ) {
				$data['roles'][] = array(
					'role' => $role_item,
					'type' => 'item',
				);
				wc_update_order_item_meta( $item_key, '_um_woo_item_assign_role', $role_item );
			}
		}

		$role_order = UM()->options()->get( 'woo_oncomplete_role' );
		if ( ! empty( $role_order ) ) {
			$data['roles'][] = array(
				'role' => $role_order,
				'type' => 'order',
			);

			$order->update_meta_data( '_um_woo_order_assign_role', $role_order );
			$order->save();
		}

		if ( empty( $data ) ) {
			return $return;
		}

		$data['user_id'] = $user_id;

		return $data;
	}

	/**
	 * Check single product order need or not need to change user role
	 *
	 * @param int $order_id
	 *
	 * @return array|bool
	 */
	public function change_role_data_single_refund( $order_id ) {
		$order   = new \WC_Order( $order_id );
		$user_id = $order->get_user_id();

		$role = UM()->options()->get( 'woo_onrefund_role' );
		if ( ! empty( $role ) && ! user_can( $user_id, $role ) ) {
			return $role;
		}

		return false;
	}

	/**
	 * Get Order Data via AJAX
	 */
	public function ajax_get_order() {
		UM()->check_ajax_nonce();

		if ( ! isset( $_POST['order_id'] ) || ! is_user_logged_in() ) {
			wp_send_json_error();
		}

		$order_id    = absint( $_POST['order_id'] );
		$order       = wc_get_order( $order_id );
		$customer_id = $order->get_user_id() ? $order->get_user_id() : absint( $order->get_meta( '_customer_user', true ) );

		if ( $customer_id !== get_current_user_id() ) {
			wp_send_json_error();
		}
		um_fetch_user( get_current_user_id() );

		$notes = $order->get_customer_order_notes();

		// Filter order actions.
		add_filter( 'woocommerce_my_account_my_orders_actions', array( $this, 'order_actions' ), 20, 2 );

		// Filter related subscrioption view URL.
		add_filter( 'wcs_get_view_subscription_url', array( $this, 'subscription_view_url' ), 20, 2 );

		$t_args = compact( 'order', 'order_id', 'notes' );
		$output = UM()->get_template( 'order-popup.php', um_woocommerce_plugin, $t_args );

		wp_send_json_success( $output );
	}

	/**
	 * Get Subscription Data via AJAX
	 * @see file /wp-content/plugins/woocommerce-subscriptions-master/woocommerce-subscriptions.php method enqueue_frontend_scripts()
	 */
	public function ajax_get_subscription() {
		UM()->check_ajax_nonce();

		// phpcs:disable WordPress.Security.NonceVerification -- already verified here
		if ( empty( $_POST['subscription_id'] ) ) {
			wp_send_json_error();
		}

		$subscription_id = absint( $_POST['subscription_id'] );
		$subscription    = wcs_get_subscription( $subscription_id );

		if ( $subscription && current_user_can( 'view_order', $subscription->get_id() ) ) {
			set_query_var( 'view-subscription', $subscription_id );

			// Filter related order view URL.
			add_filter( 'woocommerce_get_view_order_url', array( $this, 'order_view_url' ), 20, 2 );

			// Filter the "Change address" link.
			add_filter( 'woocommerce_get_endpoint_url', array( $this, 'subscription_endpoint_url' ), 20, 4 );

			// Filter the "Renew now" link.
			add_filter( 'woocommerce_subscriptions_get_early_renewal_url', array( $this, 'subscription_get_early_renewal_url' ), 20, 2 );

			// Filter the "Resubscribe" link.
			add_filter( 'wcs_users_resubscribe_link', array( $this, 'subscription_resubscribe_link' ), 20, 2 );

			$t_args = compact( 'subscription', 'subscription_id' );
			$html   = UM()->get_template( 'subscription.php', um_woocommerce_plugin, $t_args );

			$script_params = array(
				'ajax_url'               => esc_url( WC()->ajax_url() ),
				'subscription_id'        => $subscription->get_id(),
				'add_payment_method_msg' => __( 'To enable automatic renewals for this subscription, you will first need to add a payment method.', 'um-woocommerce' ) . "\n\n" . __( 'Would you like to add a payment method now?', 'um-woocommerce' ),
				'auto_renew_nonce'       => wp_create_nonce( "toggle-auto-renew-{$subscription->get_id()}" ),
				'add_payment_method_url' => esc_url( $subscription->get_change_payment_method_url() ),
				'has_payment_gateway'    => $subscription->has_payment_gateway() && wc_get_payment_gateway_by_order( $subscription )->supports( 'subscriptions' ),
			);

			$script_filepath1 = plugin_dir_path( \WC_Subscriptions::$plugin_file ) . 'assets/js/frontend/view-subscription.js';
			$script_filepath2 = plugin_dir_path( \WC_Subscriptions::$plugin_file ) . 'vendor/woocommerce/subscriptions-core/assets/js/frontend/view-subscription.js';

			if ( is_file( $script_filepath1 ) ) {
				$script_file_content = file_get_contents( $script_filepath1, true );
			} elseif ( is_file( $script_filepath2 ) ) {
				$script_file_content = file_get_contents( $script_filepath2, true );
			}

			$script_ready = empty( $script_file_content ) ? '' : preg_replace( array( '/^[^\{]+\{/', '/\}[^\}]+$/', '/\$\(/', '/\$\./' ), array( '', '', 'jQuery(', 'jQuery.' ), $script_file_content );

			$output = array(
				'content'       => preg_replace( array( '/\t+/m', '/^\s+/m' ), ' ', $html ),
				'script_ready'  => preg_replace( array( '/\t+/m', '/^\s+/m' ), ' ', $script_ready ),
				'script_params' => apply_filters( 'woocommerce_subscriptions_frontend_view_subscription_script_parameters', $script_params ),
			);
			// phpcs:enable WordPress.Security.NonceVerification -- already verified here
			wp_send_json_success( $output );
		}
	}

	/**
	 * Refresh address via AJAX
	 *
	 * @throws Exception
	 * @version 2.2.3
	 */
	public function ajax_refresh_address() {
		UM()->check_ajax_nonce();

		// phpcs:disable WordPress.Security.NonceVerification
		$form_id = isset( $_POST['form_id'] ) ? absint( $_POST['form_id'] ) : 0;
		$country = isset( $_POST['country'] ) ? sanitize_text_field( wp_unslash( $_POST['country'] ) ) : '';
		$type    = isset( $_POST['type'] ) ? sanitize_key( $_POST['type'] ) : '';
		// phpcs:enable WordPress.Security.NonceVerification
		$key = ( 'billing_country' === $type ) ? 'billing_state' : 'shipping_state';

		// set WooCommerce label for the state field.
		add_filter(
			"um_get_field__{$key}",
			static function( $array ) use ( $country ) {
				$locale = WC()->countries->get_country_locale();
				if ( isset( $locale[ $country ]['state']['label'] ) ) {
					$array['label'] = $locale[ $country ]['state']['label'];

					$asterisk = UM()->options()->get( 'form_asterisk' );
					if ( $asterisk && ! empty( $locale[ $country ]['required'] ) && true === $locale[ $country ]['required'] ) {
						$array['label'] .= '<span class="um-req" title="' . esc_attr__( 'Required', 'um-woocommerce' ) . '">*</span>';
					}
				}
				return $array;
			},
			20
		);

		// alphabetical order for states.
		add_filter(
			"um_select_dropdown_dynamic_options_{$key}",
			static function ( $options ) {
				asort( $options );
				return $options;
			},
			20
		);

		// get fields.
		if ( $form_id ) {
			UM()->fields()->set_id   = $form_id;
			UM()->fields()->set_mode = get_post_meta( $form_id, '_um_mode', true );
			UM()->fields()->editing  = true;

			$fields = UM()->query()->get_attr( 'custom_fields', $form_id ); // get state field from the form.

			if ( empty( $fields[ $key ] ) ) {
				wp_send_json_error( __( 'Can not find state field in this form.', 'um-woocommerce' ) );
			}
		} else {
			UM()->fields()->set_id   = 0;
			UM()->fields()->set_mode = 'profile';
			UM()->fields()->editing  = true;

			$fields = UM()->builtin()->get_specific_fields( $key ); // get predefined state field.
		}

		if ( ! empty( WC()->countries->get_states( $country ) ) ) {
			$fields[ $key ]['input'] = 'select';
			$fields[ $key ]['type']  = 'select';
		} else {
			$fields[ $key ]['input'] = 'text';
			$fields[ $key ]['type']  = 'text';
		}

		$html = UM()->fields()->edit_field( $key, $fields[ $key ] );
		wp_send_json_success( preg_replace( array( '/\r\n/m', '/\t/m', '/\s+/m' ), ' ', $html ) );
	}

	/**
	 * Filter order actions.
	 *
	 * Hook woocommerce_my_account_my_orders_actions - 20
	 *
	 * @since 2.4.4
	 *
	 * @param array     $actions Order actions.
	 * @param \WC_Order $order   Order instance.
	 * @return array
	 */
	public function order_actions( $actions, $order ) {
		$order_id = $order->get_id();

		// Integration with the "WooCommerce PDF Invoices" plugin.
		// Add a PDF link to the subscription "Related Orders" table.
		if ( defined( 'PDFVERSION' ) && is_a( $order, 'WC_Order' ) && $order->get_meta( '_invoice_number', true ) ) {
			$actions['pdf'] = array(
				'url'  => add_query_arg( 'pdfid', $order_id, UM()->account()->tab_link( 'orders' ) ),
				'name' => __( apply_filters( 'woocommerce_pdf_my_account_button_label', __( 'PDF Invoice', 'um-woocommerce' ) ) ),
			);
		}

		// Change or remove the order "View" action.
		if ( array_key_exists( 'view', $actions ) ) {
			if ( isset( $_POST['action'] ) && 'um_woocommerce_get_subscription' === $_POST['action'] ) {
				$actions['view']['url'] = UM()->account()->tab_link( 'orders' ) . "#$order_id";
			} else {
				unset( $actions['view'] );
			}
		}

		return $actions;
	}

	/**
	 * Change the order view link.
	 *
	 * Hook woocommerce_get_view_order_url - 20
	 *
	 * @since 2.4.4
	 *
	 * @param string    $view_order_url URL to view an order.
	 * @param \WC_Order $order          Order instance.
	 * @return string
	 */
	public function order_view_url( $view_order_url, $order ) {
		if ( ! is_account_page() && um_user( 'woo_account_orders' ) ) {
			$order_id       = $order->get_id();
			$view_order_url = UM()->account()->tab_link( 'orders' ) . "#$order_id";
		}
		return $view_order_url;
	}

	/**
	 * Change the subscription view link.
	 *
	 * Hook wcs_get_view_subscription_url - 20
	 *
	 * @since 2.4.4
	 *
	 * @param string $view_subscription_url URL to view a subscription.
	 * @param int    $subscription_id       Subscription ID.
	 * @return string
	 */
	public function subscription_view_url( $view_subscription_url, $subscription_id ) {
		if ( ! is_account_page() && um_user( 'woo_account_subscription' ) ) {
			$view_subscription_url = UM()->account()->tab_link( 'subscription' ) . "#$subscription_id";
		}
		return $view_subscription_url;
	}

	/**
	 * Filter endpoint URL for the "Change address" link.
	 *
	 * Hook woocommerce_get_endpoint_url - 20
	 *
	 * @since 2.2.5
	 *
	 * @param string $url       URL for an endpoint, which varies depending on permalink settings.
	 * @param string $endpoint  Endpoint slug.
	 * @param string $value     Query param value.
	 * @param string $permalink Permalink.
	 * @return string
	 */
	public function subscription_endpoint_url( $url, $endpoint, $value = '', $permalink = '' ) {
		if ( get_option( 'woocommerce_myaccount_edit_address_endpoint', 'edit-address' ) === $endpoint ) {
			if ( $value ) {
				$permalink = UM()->account()->tab_link( $value );
			} else {
				$permalink = UM()->account()->tab_link( 'billing' );
			}
			$url = trailingslashit( $permalink );
		}
		return $url;
	}

	/**
	 * Filter the "Renew now" link.
	 *
	 * Hook woocommerce_subscriptions_get_early_renewal_url - 20
	 *
	 * @since 2.4.4
	 *
	 * @param string $url             The early renewal URL.
	 * @param int    $subscription_id The ID of the subscription to renew to.
	 * @return string
	 */
	public function subscription_get_early_renewal_url( $url, $subscription_id ) {
		$args = array(
			'subscription_renewal_early' => $subscription_id,
			'subscription_renewal'       => 'true',
		);
		return add_query_arg( $args, UM()->account()->tab_link( 'subscription' ) );
	}

	/**
	 * Filter the "Resubscribe" link.
	 *
	 * Hook wcs_users_resubscribe_link - 20
	 *
	 * @since 2.4.4
	 *
	 * @param string $resubscribe_link The renew a subscription URL.
	 * @param int    $subscription_id  The ID of the subscription to renew to.
	 * @return string
	 */
	public function subscription_resubscribe_link( $resubscribe_link, $subscription_id ) {
		$args = array(
			'resubscribe' => $subscription_id,
		);
		$resubscribe_link = add_query_arg( $args, UM()->account()->tab_link( 'subscription' ) );
		$resubscribe_link = wp_nonce_url( $resubscribe_link, $subscription_id );
		return $resubscribe_link;
	}

	/**
	 * Check if current user has subscriptions and return subscription IDs
	 * @param  int       $user_id
	 * @param  string    $product_id
	 * @param  string    $status
	 * @param  array|int $except_subscriptions
	 *
	 * @return array|bool Subscription products ids
	 */
	public function user_has_subscription( $user_id = 0, $product_id = '', $status = 'any', $except_subscriptions = array() ) {
		if ( ! function_exists( 'wcs_get_users_subscriptions' ) ) {
			return '';
		}

		$subscriptions    = wcs_get_users_subscriptions( $user_id );
		$has_subscription = false;
		$arr_product_ids  = array();
		if ( empty( $product_id ) ) { // Any subscription
			if ( ! empty( $status ) && 'any' !== $status ) { // We need to check for a specific status
				foreach ( $subscriptions as $subscription ) {
					if ( in_array( $subscription->get_id(), (array) $except_subscriptions ) ) {
						continue;
					}
					if ( $subscription->has_status( $status ) ) {
						$order_items = $subscription->get_items();
						foreach ( $order_items as $order ) {
							$arr_product_ids[] = wcs_get_canonical_product_id( $order );
						}
					}
				}

				return $arr_product_ids;

			} elseif ( ! empty( $subscriptions ) ) {
				$has_subscription = true;
			}
		} else {
			foreach ( $subscriptions as $subscription ) {
				if ( in_array( $subscription->get_id(), (array) $except_subscriptions ) ) {
					continue;
				}
				if ( $subscription->has_product( $product_id ) && ( empty( $status ) || 'any' === $status || $subscription->has_status( $status ) ) ) {
					$has_subscription = true;
					break;
				}
			}
		}
		return $has_subscription;
	}
}
