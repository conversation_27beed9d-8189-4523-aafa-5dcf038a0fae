<?php
namespace um_ext\um_woocommerce\core;

use WC_Product;
use WP_Query;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class WooCommerce_Access {

	/**
	 * Set TRUE if the current query retrieves posts for the WooCommerce blocks product grid and products may be restricted.
	 *
	 * @var bool
	 */
	private $product_grid;

	/**
	 * WooCommerce_Access constructor.
	 */
	public function __construct() {
		add_action( 'pre_get_posts', array( &$this, 'exclude_posts' ), 20 );
		add_filter( 'pre_get_posts', array( &$this, 'woo_pre_get_posts' ), 99 );
		add_filter( 'the_posts', array( &$this, 'woo_filter_protected_posts' ), 98, 2 );

		add_action( 'admin_init', array( &$this, 'remove_hooks' ) );
		add_action( 'init', array( &$this, 'init_hooks' ) );

		add_filter( 'um_user_permissions_filter', array( &$this, 'user_permissions_filter' ) );

		add_filter( 'woocommerce_blocks_product_grid_is_cacheable', array( &$this, 'filter_blocks_product_grid_is_cacheable' ), 20 );
		add_filter( 'woocommerce_shortcode_products_query', array( &$this, 'filter_shortcode_products_query' ), 20 );
	}

	/**
	 * Exclude restricted products from the products block query.
	 *
	 * @see   \Automattic\WooCommerce\StoreApi\Utilities\ProductQuery::get_results()
	 * @since 2.3.5
	 *
	 * @param WP_Query $query
	 */
	public function exclude_posts( $query ) {
		// Set TRUE if the current query retrieves posts for the WooCommerce block "All Products".
		$is_products_rest_route = defined( 'REST_REQUEST' ) && REST_REQUEST && isset( $GLOBALS['wp']->query_vars['rest_route'] ) && '/wc/store/v1/products' === $GLOBALS['wp']->query_vars['rest_route'];

		if ( $is_products_rest_route || true === $this->product_grid ) {
			$this->product_grid = null;
			$restricted_posts   = UM()->options()->get( 'restricted_access_post_metabox' );
			if ( ! is_array( $restricted_posts ) || empty( $restricted_posts['product'] ) ) {
				return;
			}
			if ( empty( $query->query_vars['post_type'] ) || ! is_object( $query ) || $query->is_singular() ) {
				return;
			}
			if ( current_user_can( 'administrator' ) ) {
				return;
			}

			$force         = true;
			$post_types    = is_array( $query->query_vars['post_type'] ) ? $query->query_vars['post_type'] : array( $query->query_vars['post_type'] );
			$exclude_posts = UM()->access()->exclude_posts_array( $force, $post_types );

			if ( ! empty( $exclude_posts ) ) {
				$post__not_in  = $query->get( 'post__not_in', array() );
				$post__not_in_ = array_unique( array_merge( $post__not_in, $exclude_posts ) );
				$query->set( 'post__not_in', $post__not_in_ );
			}
		}
	}

	/**
	 * Disable caching for blocks that use product grid.
	 *
	 * @see   \Automattic\WooCommerce\Blocks\BlockTypes\AbstractProductGrid::get_products()
	 * @since 2.3.5
	 *
	 * @param  bool $is_cacheable True to enable cache, false to disable cache.
	 * @return bool
	 */
	public function filter_blocks_product_grid_is_cacheable( $is_cacheable ) {
		$restricted_posts = UM()->options()->get( 'restricted_access_post_metabox' );
		if ( is_array( $restricted_posts ) && ! empty( $restricted_posts['product'] ) ) {
			$this->product_grid = true;
			$is_cacheable       = false;
		}
		return $is_cacheable;
	}

	/**
	 * Exclude restricted products from the shortcode products query.
	 *
	 * @param  array $query_args Shortcode query args prepared for WP_Query.
	 * @return array
	 */
	public function filter_shortcode_products_query( $query_args ) {
		$query_args['um_main_query'] = true;
		return $query_args;
	}

	/**
	 * Initialize hooks.
	 */
	public function init_hooks() {
		// Restrict products in the loop.
		$restricted_posts = UM()->options()->get( 'restricted_access_post_metabox' );
		if ( is_array( $restricted_posts ) && ! empty( $restricted_posts['product'] ) ) {
			add_action( 'woocommerce_before_shop_loop_item', array( &$this, 'shop_loop_item_before' ), 5 );
			add_action( 'woocommerce_after_shop_loop_item', array( &$this, 'shop_loop_item_after' ), 95 );
			add_filter( 'woocommerce_product_is_visible', array( $this, 'shop_loop_item_is_visible' ), 20, 2 );
		}
	}

	/**
	 * Restrict products in the loop: Replace Add-to-cart link.
	 *
	 * @param  string     $link    Add-to-cart link HTML.
	 * @param  WC_Product $product Product.
	 *
	 * @return string A link HTML.
	 */
	public function shop_loop_item_add_to_cart_link( $link, $product ) {
		$restriction = UM()->access()->get_post_privacy_settings( $product->get_id() );
		if ( is_array( $restriction ) && array_key_exists( '_um_noaccess_action', $restriction ) && 1 === $restriction['_um_noaccess_action'] ) {
			if ( empty( $restriction['_um_access_redirect'] ) ) {
				$redirect_url = add_query_arg( 'redirect_to', urlencode_deep( $product->get_permalink() ), um_get_core_page( 'login' ) );
				$link_text    = __( 'Login', 'um-woocommerce' );
			} elseif ( ! empty( $restriction['_um_access_redirect_url'] ) ) {
				$redirect_url = $restriction['_um_access_redirect_url'];
				$link_text    = __( 'Info', 'um-woocommerce' );
			} else {
				$redirect_url = get_home_url( get_current_blog_id() );
				$link_text    = __( 'Home', 'um-woocommerce' );
			}
			$link = sprintf(
				'<a href="%s" class="button">%s</a>',
				esc_url( $redirect_url ),
				esc_html( $link_text )
			);
		}
		return $link;
	}

	/**
	 * Restrict products in the loop: Ending.
	 *
	 * @global WC_Product $product Product.
	 * @return void
	 */
	public function shop_loop_item_after() {
		global $product;
		if ( ! UM()->access()->is_restricted( $product->get_id() ) ) {
			return;
		}
		$restriction = UM()->access()->get_post_privacy_settings( $product->get_id() );
		if ( ! is_array( $restriction ) || ! empty( $restriction['_um_access_hide_from_queries'] ) || ! array_key_exists( '_um_noaccess_action', $restriction ) ) {
			return;
		}

		if ( 1 === $restriction['_um_noaccess_action'] ) {
			remove_filter( 'woocommerce_loop_add_to_cart_link', array( $this, 'shop_loop_item_add_to_cart_link' ), 20 );
		}
	}

	/**
	 * Restrict products in the loop: Opening.
	 *
	 * @global WC_Product $product Product.
	 */
	public function shop_loop_item_before() {
		global $product;
		if ( ! UM()->access()->is_restricted( $product->get_id() ) ) {
			return;
		}
		$restriction = UM()->access()->get_post_privacy_settings( $product->get_id() );
		if ( ! is_array( $restriction ) || ! empty( $restriction['_um_access_hide_from_queries'] ) || ! array_key_exists( '_um_noaccess_action', $restriction ) ) {
			return;
		}

		if ( 1 === $restriction['_um_noaccess_action'] ) {
			add_filter( 'woocommerce_loop_add_to_cart_link', array( $this, 'shop_loop_item_add_to_cart_link' ), 20, 2 );
		}
	}

	/**
	 * Restrict products in the loop: Hide from queries.
	 *
	 * @param  bool $visible    Whether the product is visible or not.
	 * @param  int  $product_id Product ID.
	 *
	 * @return bool Whether the product is visible or not.
	 */
	public function shop_loop_item_is_visible( $visible, $product_id ) {
		if ( $visible && UM()->access()->is_restricted( $product_id ) ) {
			$restriction = UM()->access()->get_post_privacy_settings( $product_id );
			if ( is_array( $restriction ) ) {
				if ( ! empty( $restriction['_um_access_hide_from_queries'] ) || 0 === $restriction['_um_noaccess_action'] ) {
					$visible = false;
				}
			}
		}
		return $visible;
	}

	/**
	 * @param array $meta
	 *
	 * @return array
	 */
	public function user_permissions_filter( $meta ) {
		if ( ! isset( $meta['woo_purchases_tab'] ) ) {
			$meta['woo_purchases_tab'] = 1;
		}

		if ( ! isset( $meta['woo_reviews_tab'] ) ) {
			$meta['woo_reviews_tab'] = 1;
		}

		if ( ! isset( $meta['woo_account_billing'] ) ) {
			$meta['woo_account_billing'] = 1;
		}

		if ( ! isset( $meta['woo_account_shipping'] ) ) {
			$meta['woo_account_shipping'] = 1;
		}

		if ( ! isset( $meta['woo_account_orders'] ) ) {
			$meta['woo_account_orders'] = 1;
		}

		if ( ! isset( $meta['woo_account_subscription'] ) ) {
			$meta['woo_account_subscription'] = 1;
		}

		if ( ! isset( $meta['woo_account_downloads'] ) ) {
			$meta['woo_account_downloads'] = 1;
		}

		if ( ! isset( $meta['woo_account_payment_methods'] ) ) {
			$meta['woo_account_payment_methods'] = 0;
		}

		return $meta;
	}

	/**
	 * Show restrict content metabox on Shop page
	 */
	public function remove_hooks() {
		remove_filter( 'um_restrict_content_hide_metabox', array( UM()->metabox(), 'hide_metabox_restrict_content_shop' ) );
	}

	/**
	 * @param WP_Query $query
	 *
	 * @return WP_Query
	 */
	public function woo_pre_get_posts( $query ) {
		global $wp_query;

		if ( ! isset( $wp_query ) ) {
			return $query;
		}

		//is_shop add notices because uses $query->is_page in wp-query
		//so added @ for hide notices, but works properly
		if ( ! ( @is_shop() && $query->is_main_query() ) ) {
			return $query;
		}

		if ( current_user_can( 'administrator' ) ) {
			return $query;
		}

		if ( empty( wc_get_page_id( 'shop' ) ) ) {
			return $query;
		}
		$shop_post = get_post( wc_get_page_id( 'shop' ) );
		if ( empty( $shop_post ) ) {
			return $query;
		}

		if ( ! UM()->access()->is_restricted( $shop_post->ID ) ) {
			return $query;
		} else {

			$restriction = UM()->access()->get_post_privacy_settings( $shop_post );

			if ( is_archive() && ! empty( $restriction['_um_access_hide_from_queries'] ) ) {
				$query->set_404();
				status_header( 404 );
				include get_query_template( '404' );
				exit;
			}

			if ( 1 === absint( $restriction['_um_accessible'] ) || 2 === absint( $restriction['_um_accessible'] ) ) {
				if ( isset( $restriction['_um_noaccess_action'] ) && 1 === absint( $restriction['_um_noaccess_action'] ) ) {
					$curr = UM()->permalinks()->get_current_url();

					if ( ! isset( $restriction['_um_access_redirect'] ) || 0 === absint( $restriction['_um_access_redirect'] ) ) {

						wp_safe_redirect( esc_url_raw( add_query_arg( 'redirect_to', urlencode_deep( $curr ), um_get_core_page( 'login' ) ) ) );
						exit;

					} elseif ( 1 === absint( $restriction['_um_access_redirect'] ) ) {

						if ( ! empty( $restriction['_um_access_redirect_url'] ) ) {
							$redirect = esc_url_raw( $restriction['_um_access_redirect_url'] );
							um_safe_redirect( $redirect );
						} else {
							$redirect = esc_url_raw( add_query_arg( 'redirect_to', urlencode_deep( $curr ), um_get_core_page( 'login' ) ) );
							wp_safe_redirect( $redirect );
							exit;
						}
					}
				}
			}
		}

		return $query;
	}

	/**
	 * Protect Post Types in query
	 * Restrict content new logic
	 *
	 * @param $posts
	 * @param WP_Query $query
	 * @return array
	 */
	public function woo_filter_protected_posts( $posts, $query ) {
		global $wp_query;

		if ( ! isset( $wp_query ) ) {
			return $posts;
		}

		//is_shop add notices because uses $query->is_page in wp-query
		//so added @ for hide notices, but works properly
		if ( ! ( @is_shop() && $query->is_main_query() ) ) {
			return $posts;
		}

		if ( empty( wc_get_page_id( 'shop' ) ) ) {
			return $posts;
		}
		$shop_post = get_post( wc_get_page_id( 'shop' ) );
		if ( empty( $shop_post ) ) {
			return $posts;
		}

		$restriction = UM()->access()->get_post_privacy_settings( $shop_post );

		if ( ! $restriction ) {
			return $posts;
		}

		//post is private
		if ( '1' == $restriction['_um_accessible'] ) {
			//if post for not logged-in users and user is not logged in
			if ( ! is_user_logged_in() ) {
				return $posts;
			} else {

				if ( current_user_can( 'administrator' ) ) {
					return $posts;
				}

				//if single post query
				if ( ! isset( $restriction['_um_noaccess_action'] ) || '0' == $restriction['_um_noaccess_action'] ) {
					return $this->clear_query();
				}
			}
		} elseif ( '2' == $restriction['_um_accessible'] ) {
			// If post for logged-in users and user is not logged in
			if ( ! is_user_logged_in() ) {

				if ( ! isset( $restriction['_um_noaccess_action'] ) || '0' == $restriction['_um_noaccess_action'] ) {
					return $this->clear_query();
				}

			} else {

				if ( current_user_can( 'administrator' ) ) {
					return $posts;
				}

				$custom_restrict = apply_filters( 'um_custom_restriction', true, $restriction );

				if ( empty( $restriction['_um_access_roles'] ) ) {
					if ( $custom_restrict ) {
						return $posts;
					}
				} else {
					$user_can = UM()->access()->user_can( get_current_user_id(), $restriction['_um_access_roles'] );

					if ( isset( $user_can ) && $user_can && $custom_restrict ) {
						return $posts;
					}
				}

				//if single post query
				if ( ! isset( $restriction['_um_noaccess_action'] ) || '0' == $restriction['_um_noaccess_action'] ) {
					return $this->clear_query();
				}
			}
		}

		return $posts;
	}

	/**
	 * Clear Shop page content when there are not products
	 *
	 * @return array
	 */
	public function clear_query() {
		remove_action( 'woocommerce_no_products_found', 'wc_no_products_found' );
		add_action( 'woocommerce_no_products_found', array( &$this, 'um_wc_access_message' ) );
		return array();
	}

	/**
	 * Show restriction message on shop page
	 */
	public function um_wc_access_message() {
		$post_id = wc_get_page_id( 'shop' );

		$restricted_global_message = UM()->options()->get( 'restricted_access_message' );

		$restriction = UM()->access()->get_post_privacy_settings( get_post( $post_id ) );

		$message = '';
		//post is private
		if ( ! isset( $restriction['_um_restrict_by_custom_message'] ) || '0' == $restriction['_um_restrict_by_custom_message'] ) {
			$message = $restricted_global_message;
		} elseif ( '1' == $restriction['_um_restrict_by_custom_message'] ) {
			$message = ! empty( $restriction['_um_restrict_custom_message'] ) ? $restriction['_um_restrict_custom_message'] : '';
		}

		echo $message;
	}
}
