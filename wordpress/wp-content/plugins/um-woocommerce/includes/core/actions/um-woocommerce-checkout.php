<?php
/**
 * Hooks that influence the WooCommerce Checkout page,
 *
 * @package um_ext\um_woocommerce\core
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * If checkout registration is disabled and not logged in, the user cannot checkout. Display a sign up link.
 *
 * @param WC_Checkout $checkout  The WooCommerce checkout class.
 */
function um_woocommerce_checkout_register( $checkout ) {

	if ( ! $checkout->is_registration_enabled() && $checkout->is_registration_required() && ! is_user_logged_in() && function_exists( 'wc_print_notice' ) ) {
		$curr = UM()->permalinks()->get_current_url( true );
		$link = add_query_arg( 'redirect_to', urlencode_deep( $curr ), um_get_core_page( 'register' ) );
		?>
		<div class="um-woocommerce-checkout-register-message">
			<?php wc_print_notice( apply_filters( 'woocommerce_checkout_must_be_logged_in_message', esc_html__( 'You must be logged in to checkout.', 'woocommerce' ) ) . ' ' . apply_filters( 'um_woocommerce_checkout_register_message', esc_html__( 'New customer?', 'um-woocommerce' ) ) . ' <a href="' . esc_url( $link ) . '" class="um-woocommerce-checkout-register-link">' . esc_html__( 'Click here to register', 'um-woocommerce' ) . '</a>', 'notice' ); ?>
		</div>
		<?php
		add_filter( 'woocommerce_checkout_must_be_logged_in_message', '__return_false', 20 );
	}
}

add_action( 'woocommerce_before_checkout_form', 'um_woocommerce_checkout_register', 20 );
