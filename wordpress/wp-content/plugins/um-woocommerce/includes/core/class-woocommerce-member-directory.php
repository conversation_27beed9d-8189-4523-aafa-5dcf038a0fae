<?php
namespace um_ext\um_woocommerce\core;

use Automattic\WooCommerce\Utilities\OrderUtil;
use WC_Countries;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class WooCommerce_Member_Directory
 *
 * @package um_ext\um_woocommerce\core
 */
class WooCommerce_Member_Directory {

	/**
	 * WooCommerce_Member_Directory constructor.
	 */
	public function __construct() {
		add_filter( 'um_members_directory_filter_fields', array( &$this, 'members_directory_filter_fields' ) );
		add_filter( 'um_members_directory_filter_types', array( &$this, 'directory_filter_types' ) );
		add_filter( 'um_search_fields', array( &$this, 'directory_filter_state' ), 10, 2 );

		add_filter( 'um_member_directory_filter_woo_order_count_slider', array( &$this, 'um_woocommerce_directory_filter_woo_order_count_slider' ) );
		add_filter( 'um_member_directory_filter_woo_total_spent_slider', array( &$this, 'um_woocommerce_directory_filter_woo_total_spent_slider' ) );
		add_filter( 'um_member_directory_filter_slider_range_placeholder', array( &$this, 'slider_range_placeholder' ), 10, 2 );

		add_filter( 'um_search_fields', array( $this, 'country_dropdown' ) );

		add_filter( 'um_query_args_woo_order_count__filter', array( $this, 'filter_by_orders_count' ), 10, 3 );
		add_filter( 'um_query_args_woo_order_count__filter_meta', array( $this, 'filter_by_orders_count_meta' ), 10, 6 );
		add_filter( 'um_query_args_woo_total_spent__filter', array( $this, 'filter_by_total_spent' ), 10, 3 );
		add_filter( 'um_query_args_woo_total_spent__filter_meta', array( $this, 'filter_by_total_spent_meta' ), 10, 6 );

		add_filter( 'um_search_fields', array( &$this, 'change_filter_label' ), 10, 2 );

		add_filter( 'um_ajax_get_members_data', array( &$this, 'get_members_data' ), 50, 2 );

		add_filter( 'um_member_directory_filter_select_options', array( &$this, 'intersect_filter_options' ), 10, 3 );
	}

	/**
	 * @param $options
	 * @param $values_array
	 * @param $attrs
	 *
	 * @return array
	 */
	public function intersect_filter_options( $options, $values_array, $attrs ) {
		if ( ! in_array( $attrs['metakey'], array( 'billing_country', 'shipping_country' ), true ) ) {
			return $options;
		}

		$fields = UM()->builtin()->all_user_fields;
		$attrs  = $fields[ $attrs['metakey'] ];
		$attrs  = apply_filters( 'um_search_fields', $attrs, $attrs['metakey'] );

		if ( ! empty( $values_array ) ) {
			$values_array = array_map( 'maybe_unserialize', $values_array );
			$temp_values  = array();
			$temp_values2 = array();
			foreach ( $values_array as $values ) {
				if ( is_array( $values ) ) {
					$temp_values2[] = $values;
				} else {
					$temp_values[] = $values;
				}
			}
			$temp_values2 = array_merge( ...$temp_values2 );
			$temp_values  = array_merge( $temp_values, $temp_values2 );
			$values_array = array_unique( $temp_values );
		}

		return array_intersect_key( array_map( 'trim', $attrs['options'] ), array_flip( $values_array ) );
	}

	/**
	 * Filter members by the "Total Orders" directory filter.
	 * Custom table for usermeta is disabled.
	 *
	 * @version 2.3.8 Added HPOS support.
	 *
	 * @param $query
	 * @param $field
	 * @param $value
	 *
	 * @return bool
	 */
	public function filter_by_orders_count( $query, $field, $value ) {
		$user_ids = $this->get_filtered_user_ids( $value );

		if ( ! empty( $user_ids ) ) {
			UM()->member_directory()->query_args['include'] = $user_ids;
		} else {
			UM()->member_directory()->query_args['include'] = array( '0' );
		}

		UM()->member_directory()->custom_filters_in_query[ $field ] = $value;

		return true;
	}

	/**
	 * Get users by the number of orders.
	 *
	 * @see \um_ext\um_woocommerce\core\WooCommerce_Member_Directory::filter_by_orders_count()
	 * @see \um_ext\um_woocommerce\core\WooCommerce_Member_Directory::filter_by_orders_count_meta()
	 *
	 * @global \wpdb $wpdb WordPress database access abstraction class.
	 *
	 * @param array $value An array with two numeric items:
	 * - the first item is the minimal limit of orders.
	 * - the second item is the maximal limit of orders.
	 *
	 * @return array User IDs.
	 */
	private function get_filtered_user_ids( $value ) {
		global $wpdb;

		$statuses = array_map( 'esc_sql', wc_get_is_paid_statuses() );
		foreach ( $statuses as &$status ) {
			if ( false === strpos( $status, 'wc-' ) ) {
				$status = 'wc-' . $status;
			}
		}
		unset( $status );

		$min = (int) min( $value );
		$max = (int) max( $value );

		$hpos        = OrderUtil::custom_orders_table_usage_is_enabled();
		$order_types = implode( "','", wc_get_order_types( 'order-count' ) );
		$statuses    = implode( "','", $statuses );

		// phpcs:disable WordPress.DB.PreparedSQL.InterpolatedNotPrepared -- This query cannot use interpolation.
		if ( $min === $max ) {
			if ( 0 === $min ) {
				if ( $hpos ) {
					// HPOS usage is enabled.
					$user_ids = $wpdb->get_col(
						"SELECT DISTINCT u.ID
						FROM {$wpdb->users} AS u
						LEFT JOIN {$wpdb->prefix}wc_orders AS o ON ( o.customer_id = u.ID AND o.type IN ('$order_types') AND o.status IN ('$statuses') )
						WHERE o.id IS NULL"
					);
				} else {
					// Traditional CPT-based orders are in use.
					$user_ids = $wpdb->get_col(
						"SELECT DISTINCT u.ID
						FROM {$wpdb->users} as u
						LEFT JOIN {$wpdb->postmeta} AS pm ON ( pm.meta_key = '_customer_user' AND pm.meta_value = u.ID )
						LEFT JOIN {$wpdb->posts} AS p ON ( p.ID = pm.post_id AND p.post_status IN ('$statuses') AND p.post_type IN ('$order_types') )
						WHERE pm.meta_id IS NULL AND
						      p.ID IS NULL"
					);
				}
			} else {
				if ( $hpos ) {
					// HPOS usage is enabled.
					$user_ids = $wpdb->get_col(
						$wpdb->prepare(
							"SELECT o.customer_id AS user_id
							FROM {$wpdb->prefix}wc_orders AS o
							WHERE o.type IN ('$order_types') AND
							      o.status IN ('$statuses')
							GROUP BY o.customer_id
							HAVING COUNT( o.id ) = %d",
							$min
						)
					);
				} else {
					// Traditional CPT-based orders are in use.
					$user_ids = $wpdb->get_col(
						$wpdb->prepare(
							"SELECT pm.meta_value AS user_id
							FROM $wpdb->posts as p
							LEFT JOIN {$wpdb->postmeta} AS pm ON p.ID = pm.post_id
							WHERE pm.meta_key = '_customer_user' AND
							      p.post_type IN ('$order_types') AND
							      p.post_status IN ('$statuses')
							GROUP BY pm.meta_value
							HAVING COUNT( p.ID ) = %d",
							$min
						)
					);
				}
			}
		} else {
			if ( 0 === $min ) {
				if ( $hpos ) {
					// HPOS usage is enabled.
					$user_ids1 = $wpdb->get_col(
						"SELECT DISTINCT u.ID
						FROM {$wpdb->users} AS u
						LEFT JOIN {$wpdb->prefix}wc_orders AS o ON ( o.customer_id = u.ID AND o.type IN ('$order_types') AND o.status IN ('$statuses') )
						WHERE o.id IS NULL"
					);

					$user_ids2 = $wpdb->get_col(
						$wpdb->prepare(
							"SELECT o.customer_id AS user_id
							FROM {$wpdb->prefix}wc_orders AS o
							WHERE o.type IN ('$order_types') AND
							      o.status IN ('$statuses')
							GROUP BY o.customer_id
							HAVING COUNT( o.id ) BETWEEN %d AND %d",
							$min,
							$max
						)
					);
				} else {
					// Traditional CPT-based orders are in use.
					$user_ids1 = $wpdb->get_col(
						"SELECT DISTINCT u.ID
						FROM {$wpdb->users} as u
						LEFT JOIN {$wpdb->postmeta} AS pm ON ( pm.meta_key = '_customer_user' AND pm.meta_value = u.ID )
						LEFT JOIN {$wpdb->posts} AS p ON ( p.ID = pm.post_id AND p.post_status IN ('$statuses') AND p.post_type IN ('$order_types') )
						WHERE pm.meta_id IS NULL AND
						      p.ID IS NULL"
					);

					$user_ids2 = $wpdb->get_col(
						$wpdb->prepare(
							"SELECT pm.meta_value AS user_id
							FROM $wpdb->posts as p
							LEFT JOIN {$wpdb->postmeta} AS pm ON p.ID = pm.post_id
							WHERE pm.meta_key = '_customer_user' AND
							      p.post_type IN ('$order_types') AND
							      p.post_status IN ('$statuses')
							GROUP BY pm.meta_value
							HAVING COUNT( p.ID ) BETWEEN %d AND %d",
							$min,
							$max
						)
					);
				}
				$user_ids = array_merge( $user_ids1, $user_ids2 );
			} else {
				if ( $hpos ) {
					// HPOS usage is enabled.
					$user_ids = $wpdb->get_col(
						$wpdb->prepare(
							"SELECT o.customer_id AS user_id
							FROM {$wpdb->prefix}wc_orders AS o
							WHERE o.type IN ('$order_types') AND
							      o.status IN ('$statuses')
							GROUP BY o.customer_id
							HAVING COUNT( o.id ) BETWEEN %d AND %d",
							$min,
							$max
						)
					);
				} else {
					// Traditional CPT-based orders are in use.
					$user_ids = $wpdb->get_col(
						$wpdb->prepare(
							"SELECT pm.meta_value AS user_id
							FROM $wpdb->posts as p
							LEFT JOIN {$wpdb->postmeta} AS pm ON p.ID = pm.post_id
							WHERE pm.meta_key = '_customer_user' AND
							      p.post_type IN ('$order_types') AND
							      p.post_status IN ('$statuses')
							GROUP BY pm.meta_value
							HAVING COUNT( p.ID ) BETWEEN %d AND %d",
							$min,
							$max
						)
					);
				}
			}
		}
		// phpcs:enable WordPress.DB.PreparedSQL.InterpolatedNotPrepared -- This query cannot use interpolation.

		return $user_ids;
	}

	/**
	 * Filter members by the "Total Orders" directory filter.
	 * Use custom table for usermeta.
	 *
	 * @version 2.3.8 Added HPOS support.
	 *
	 * @param $skip
	 * @param $query
	 * @param $field
	 * @param $value
	 * @param $filter_type
	 * @param bool $is_default
	 *
	 * @return bool
	 */
	public function filter_by_orders_count_meta( $skip, $query, $field, $value, $filter_type, $is_default ) {
		$user_ids = $this->get_filtered_user_ids( $value );

		if ( ! empty( $user_ids ) ) {
			$user_ids = array_map( 'absint', $user_ids );
			$user_ids = implode( "','", $user_ids );

			$query->where_clauses[] = "u.ID IN ('$user_ids')";
		} else {
			$query->where_clauses[] = "u.ID IN ('0')";
		}

		if ( ! $is_default ) {
			$query->custom_filters_in_query[ $field ] = $value;
		}

		return true;
	}

	/**
	 * Filter members by the "Total Spent" directory filter.
	 * Custom table for usermeta is disabled.
	 *
	 * @param $query
	 * @param $field
	 * @param $value
	 *
	 * @return array
	 */
	public function filter_by_total_spent( $query, $field, $value ) {
		$min = min( $value );
		$max = max( $value );

		if ( $min === $max ) {
			if ( 0 === $min ) {
				$query = array(
					'relation' => 'OR',
					array(
						'key'   => '_money_spent',
						'value' => $min,
					),
					array(
						'key'     => '_money_spent',
						'compare' => 'NOT EXISTS',
					),
				);
			} else {
				$query = array(
					'key'   => '_money_spent',
					'value' => $min,
				);
			}
		} else {
			if ( 0 === $min ) {
				$query = array(
					'relation' => 'OR',
					array(
						'key'       => '_money_spent',
						'value'     => array_map( 'absint', $value ),
						'compare'   => 'BETWEEN',
						'type'      => 'NUMERIC',
						'inclusive' => true,
					),
					array(
						'key'     => '_money_spent',
						'compare' => 'NOT EXISTS',
					),
				);
			} else {
				$query = array(
					'key'       => '_money_spent',
					'value'     => array_map( 'absint', $value ),
					'compare'   => 'BETWEEN',
					'type'      => 'NUMERIC',
					'inclusive' => true,
				);
			}
		}

		UM()->member_directory()->custom_filters_in_query[ $field ] = $value;

		return $query;
	}

	/**
	 * Filter members by the "Total Spent" directory filter.
	 * Use custom table for usermeta.
	 *
	 * @param $skip
	 * @param $query
	 * @param $field
	 * @param $value
	 * @param $filter_type
	 * @param bool $is_default
	 *
	 * @return bool
	 */
	public function filter_by_total_spent_meta( $skip, $query, $field, $value, $filter_type, $is_default ) {
		global $wpdb;

		$min = min( $value );
		$max = max( $value );

		$query->joins[] = "LEFT JOIN {$wpdb->prefix}um_metadata ummwoo ON ( ummwoo.user_id = u.ID AND ummwoo.um_key = '_money_spent' )";

		if ( $min === $max ) {
			if ( 0 === $min ) {
				$query->where_clauses[] = 'ummwoo.um_value IS NULL';
			} else {
				$query->where_clauses[] = $wpdb->prepare( 'CAST( ummwoo.um_value AS UNSIGNED ) = %d', $min );
			}
		} else {
			if ( 0 === $min ) {
				$query->where_clauses[] = $wpdb->prepare( '( ummwoo.um_value IS NULL OR CAST( ummwoo.um_value AS UNSIGNED ) BETWEEN %d AND %d )', $min, $max );
			} else {
				$query->where_clauses[] = $wpdb->prepare( 'CAST( ummwoo.um_value AS UNSIGNED ) BETWEEN %d AND %d', $min, $max );
			}
		}

		if ( ! $is_default ) {
			$query->custom_filters_in_query[ $field ] = $value;
		}

		return true;
	}

	/**
	 * @param $options
	 *
	 * @return mixed
	 */
	public function members_directory_filter_fields( $options ) {
		$options['billing_city']     = __( 'WC Billing city', 'um-woocommerce' );
		$options['billing_country']  = __( 'WC Billing country', 'um-woocommerce' );
		$options['shipping_city']    = __( 'WC Shipping city', 'um-woocommerce' );
		$options['shipping_country'] = __( 'WC Shipping country', 'um-woocommerce' );
		$options['woo_order_count']  = __( 'Total Orders', 'um-woocommerce' );
		$options['woo_total_spent']  = __( 'Total Spent', 'um-woocommerce' );
		$options['billing_state']    = __( 'WC Billing state', 'um-woocommerce' );
		$options['shipping_state']   = __( 'WC Shipping state', 'um-woocommerce' );

		return $options;
	}

	/**
	 * @param $filters
	 *
	 * @return mixed
	 */
	public function directory_filter_types( $filters ) {
		$filters['billing_country']  = 'select';
		$filters['shipping_country'] = 'select';
		$filters['woo_order_count']  = 'slider';
		$filters['woo_total_spent']  = 'slider';
		$filters['billing_state']    = 'select';
		$filters['shipping_state']   = 'select';

		return $filters;
	}

	/**
	 * Member directory filters `WC Billing state` and `WC Shipping state`
	 *
	 * @since  2.2.7
	 *
	 * @param  array  $attrs     Field data.
	 * @param  string $field_key Meta key of the parent country field.
	 *
	 * @return array
	 */
	public function directory_filter_state( $attrs, $field_key ) {
		if ( in_array( $field_key, array( 'billing_state', 'shipping_state' ), true ) ) {
			$attrs['custom_dropdown_options_source'] = 'um_woo_directory_get_states';
			$attrs['parent_dropdown_relationship']   = str_replace( 'state', 'country', $field_key );
			$attrs['type']                           = 'multiselect';
		}
		return $attrs;
	}

	/**
	 * Get range limits for the "Total Orders" directory filter.
	 *
	 * @version 2.3.8 Added HPOS support.
	 *
	 * @param array $range
	 *
	 * @return array|bool
	 */
	public function um_woocommerce_directory_filter_woo_order_count_slider( $range ) {
		global $wpdb;

		$statuses = array_map( 'esc_sql', wc_get_is_paid_statuses() );
		foreach ( $statuses as &$status ) {
			if ( false === strpos( $status, 'wc-' ) ) {
				$status = 'wc-' . $status;
			}
		}
		unset( $status );

		$order_types = implode( "','", wc_get_order_types( 'order-count' ) );
		$statuses    = implode( "','", $statuses );

		// phpcs:disable WordPress.DB.PreparedSQL.InterpolatedNotPrepared -- This query cannot use interpolation.
		if ( OrderUtil::custom_orders_table_usage_is_enabled() ) {
			// HPOS usage is enabled.
			$counts = $wpdb->get_col(
				"SELECT COUNT( id )
				FROM {$wpdb->prefix}wc_orders
				WHERE type IN ('$order_types') AND
					  status IN ('$statuses')
				GROUP BY customer_id"
			);
		} else {
			// Traditional CPT-based orders are in use.
			$counts = $wpdb->get_col(
				"SELECT COUNT( p.ID ) as woo_orders
				FROM $wpdb->posts as p
				LEFT JOIN {$wpdb->postmeta} AS pm ON p.ID = pm.post_id
				WHERE pm.meta_key = '_customer_user' AND
					  p.post_type IN ('$order_types') AND
					  p.post_status IN ('$statuses')
				GROUP BY pm.meta_value
				ORDER BY woo_orders"
			);
		}
		// phpcs:enable WordPress.DB.PreparedSQL.InterpolatedNotPrepared -- This query cannot use interpolation.

		return empty( $counts ) ? false : array( 0, max( $counts ) );
	}

	/**
	 * Get range limits for the "Total Spent" directory filter.
	 *
	 * @param array $range
	 *
	 * @return array|bool
	 */
	public function um_woocommerce_directory_filter_woo_total_spent_slider( $range ) {
		global $wpdb;

		$meta = $wpdb->get_row(
			"SELECT MAX( CAST( meta_value AS UNSIGNED ) ) as max_meta,
			COUNT( DISTINCT meta_value ) as amount
			FROM {$wpdb->usermeta}
			WHERE meta_key = '_money_spent'",
			ARRAY_A
		);

		return empty( $meta['max_meta'] ) ? false : array( 0, $meta['max_meta'] );
	}

	/**
	 * @param array  $placeholder
	 * @param string $filter
	 *
	 * @return array
	 */
	public function slider_range_placeholder( $placeholder, $filter ) {
		if ( ! in_array( $filter, array( 'woo_order_count', 'woo_total_spent' ), true ) ) {
			return $placeholder;
		}

		if ( 'woo_order_count' === $filter ) {
			$label = __( 'Total Orders:', 'um-woocommerce' );
		} else {
			// translators: %s means the Woocommerce symbol.
			$label = sprintf( __( 'Total Spent (%s):', 'um-woocommerce' ), get_woocommerce_currency_symbol() );
		}

		return array(
			'<strong>' . $label . '</strong>&nbsp;{value}',
			'<strong>' . $label . '</strong>&nbsp;{min_range} - {max_range}',
		);
	}

	/**
	 * @param array $attrs
	 *
	 * @return array
	 */
	public function country_dropdown( $attrs ) {
		if ( isset( $attrs['metakey'] ) && in_array( $attrs['metakey'], array( 'billing_country', 'shipping_country' ), true ) ) {
			$countries_obj = new WC_Countries();
			$countries     = $countries_obj->__get( 'countries' );

			$attrs['options'] = $countries;
			$attrs['custom']  = true;
		}

		return $attrs;
	}

	/**
	 * Remove "WC " from Woo address fields labels
	 *
	 * @param array $attrs
	 *
	 * @return array
	 */
	public function change_filter_label( $attrs, $field_key ) {
		$address_field_keys = UM()->WooCommerce_API()->api()->get_wc_address_fields( true );

		if ( in_array( $field_key, $address_field_keys, true ) ) {
			$attrs['label'] = substr( $attrs['label'], 3 );
		}
		return $attrs;
	}

	/**
	 * Expand AJAX member directory data
	 *
	 * @param array $data_array
	 * @param int   $user_id
	 *
	 * @return array
	 */
	public function get_members_data( $data_array, $user_id ) {
		if ( isset( $data_array['billing_country'] ) || isset( $data_array['shipping_country'] ) ) {
			$countries = UM()->builtin()->get( 'countries' );

			if ( isset( $data_array['billing_country'] ) && 2 === strlen( $data_array['billing_country'] ) ) {
				$lang_code                     = $data_array['billing_country'];
				$data_array['billing_country'] = $countries[ $lang_code ];
			}
			if ( isset( $data_array['shipping_country'] ) && 2 === strlen( $data_array['shipping_country'] ) ) {
				$lang_code                      = $data_array['shipping_country'];
				$data_array['shipping_country'] = $countries[ $lang_code ];
			}
		}

		return $data_array;
	}
}
