<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}


/**
 * Replace placeholders {um_profile_url} and {um_profile_link} in WooCommerce emails.
 *
 * Can be disabled with: remove_filter( 'woocommerce_email_format_string', 'um_woocommerce_email_format_string', 10 );
 *
 * @since 2.3.9
 *
 * @param string   $string    Text to replace placeholders in.
 * @param WC_Email $wc_email  WooCommerce Email class.
 *
 * @return string
 */
function um_woocommerce_email_format_string( $string, $wc_email ) {

	if ( is_a( $wc_email->object, 'WC_Order' ) ) {
		$order        = $wc_email->object;
		$profile_url  = um_user_profile_url( $order->get_customer_id() );
		$profile_link = '<a href="' . esc_url( $profile_url ) . '" target="_blank">' . esc_html__( 'View profile &rarr;', 'um-woocommerce' ) . '</a>';

		$find = array(
			'{um_profile_url}',
			'{um_profile_link}',
		);

		$replace = array(
			$profile_url,
			$profile_link,
		);

		$string = str_replace( $find, $replace, $string );
	}

	return $string;
}
add_filter( 'woocommerce_email_format_string', 'um_woocommerce_email_format_string', 10, 2 );
