<?php if ( ! defined( 'ABSPATH' ) ) exit;


/**
 * Hide "Add to cart" button
 *
 * @param $is_purchasable
 * @param $product
 *
 * @return boolean
 */
function um_woocommerce_hide_add_to_cart_button( $is_purchasable, $product ) {
	if ( UM()->options()->get( 'woo_hide_add_to_cart_button' ) ) {
		$woo_hide_add_to_cart_button_options = UM()->options()->get( 'woo_hide_add_to_cart_button_options' );
		if ( $woo_hide_add_to_cart_button_options == 0 ) {
			return false;
		} elseif ( $woo_hide_add_to_cart_button_options == 1 ) {
			if ( ! is_user_logged_in() ) {
				return false;
			}
		} elseif ( $woo_hide_add_to_cart_button_options == 2 ) {
			if ( is_user_logged_in() ) {
				$woo_hide_add_to_cart_button_roles = UM()->options()->get( 'woo_hide_add_to_cart_button_roles' );
				// hide for all logged in users
				if ( empty ( $woo_hide_add_to_cart_button_roles ) ) {
					return false;
				}

				foreach ( $woo_hide_add_to_cart_button_roles as $role_key ) {
					// hide only for selected role
					if ( current_user_can( $role_key ) ) {
						return false;
					}
				}
			}
		}
	}

	return $is_purchasable;
}
add_filter( 'woocommerce_is_purchasable', 'um_woocommerce_hide_add_to_cart_button', 10, 2 );
