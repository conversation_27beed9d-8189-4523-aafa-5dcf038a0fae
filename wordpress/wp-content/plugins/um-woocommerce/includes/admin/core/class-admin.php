<?php
namespace um_ext\um_woocommerce\admin\core;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ){
	exit;
}


if ( ! class_exists( 'um_ext\um_woocommerce\admin\core\Admin' ) ) {


	/**
	 * Class Admin
	 * @package um_ext\um_woocommerce\admin\core
	 */
	class Admin {


		/**
		 * Admin constructor.
		 */
		public function __construct() {
			add_action( 'add_meta_boxes', array( &$this, 'add_product_metabox' ), 1 );
			add_filter( 'um_admin_role_metaboxes', array( &$this, 'add_role_metabox' ), 10, 1 );
			add_action( 'save_post', array( &$this, 'save_metabox_product_settings' ), 10, 2 );

			add_action( 'admin_enqueue_scripts', array( &$this, 'admin_js' ) );

			add_filter( 'um_settings_structure', array( &$this, 'woocommerce_settings' ) );

			add_filter( 'um_override_templates_scan_files', array( &$this, 'um_woocommerce_extend_scan_files' ), 10, 1 );
			add_filter( 'um_override_templates_get_template_path__um-woocommerce', array( &$this, 'um_woocommerce_get_path_template' ), 10, 2 );

			// Displays the "View profile" link after order details on the "Edit order" page.
			add_action( 'woocommerce_admin_order_data_after_order_details', array( $this, 'display_after_order_details' ), 10, 1 );
		}

		/**
		 * Admin enqueued scripts
		 */
		public function admin_js() {
			$suffix = UM()->admin()->enqueue()::get_suffix();

			wp_register_script( 'um-woocommerce-admin', um_woocommerce_url . 'assets/js/um-woocommerce-admin' . $suffix . '.js', array( 'jquery' ), um_woocommerce_version, true );

			$screen = get_current_screen();
			if ( isset( $screen->id ) && strstr( $screen->id, 'product' ) ) {
				wp_enqueue_script( 'um-woocommerce-admin' );
			}
		}


		/**
		 * Displays the "View profile" link after order details on the "Edit order" page.
		 *
		 * Can be disabled with: remove_action( 'woocommerce_admin_order_data_after_order_details', array( UM()->WooCommerce_API()->admin(), 'display_after_order_details' ), 10 );
		 *
		 * @since 2.3.9
		 *
		 * @param WC_Order $order
		 */
		public function display_after_order_details( $order ) {
			if ( function_exists( 'um_user_profile_url' ) ) {
				$profile_url = um_user_profile_url( $order->get_customer_id() );
				?>
				<p class="form-field form-field-wide wc-customer-profile">
					<strong><?php esc_html_e( 'UM Profile:', 'um-woocommerce' ); ?></strong>
					<a href="<?php echo esc_url( $profile_url ); ?>" target="_blank"><?php esc_html_e( 'View profile &rarr;', 'um-woocommerce' ); ?></a>
				</p>
				<?php
			}
		}


		/**
		 * Extend settings
		 *
		 * @param $settings
		 *
		 * @return mixed
		 */
		public function woocommerce_settings( $settings ) {
			$settings['licenses']['fields'][] = array(
				'id'        => 'um_woocommerce_license_key',
				'label'     => __( 'Woocommerce License Key', 'um-woocommerce' ),
				'item_name' => 'WooCommerce',
				'author'    => 'Ultimate Member',
				'version'   => um_woocommerce_version,
			);

			$fields = array(
				array(
					'id'            => 'woo_oncomplete_except_roles',
					'type'          => 'select',
					'label'         => __( 'Ignore the roles update, if the user has these roles on complete/processing or refund payment', 'um-woocommerce' ),
					'tooltip'       => __( 'Only applicable if you assigned a role when order is completed/processing or refund.', 'um-woocommerce' ),
					'options'       => UM()->roles()->get_roles(),
					'placeholder'   => __( 'Community role(s)..', 'um-woocommerce' ),
					'multi'         => true,
					'size'          => 'small'
				),
				array(
					'id'            => 'woo_oncomplete_role',
					'type'          => 'select',
					'label'         => __( 'Assign this role to users when an order is completed/processing', 'um-woocommerce' ),
					'tooltip'       => __( 'Automatically set the user this role when an order\'s payment is completed.', 'um-woocommerce' ),
					'options'       => array( '' => __( 'None', 'um-woocommerce' ) ) + UM()->roles()->get_roles(),
					'placeholder'   => __( 'Community role...', 'um-woocommerce' ),
					'size'          => 'small',
				),
				array(
					'id'            => 'woo_onhold_change_roles',
					'type'          => 'select',
					'label'         => __( 'Upgrade user role when payment is on-hold before complete or processing status', 'um-woocommerce' ),
					'options'       => array( 0 => __( 'No', 'um-woocommerce' ), 1 => __( 'Yes', 'um-woocommerce' ) ),
					'size'          => 'small',
					'conditional'   => array( 'woo_oncomplete_role', '!=', '' ),
				),
				array(
					'id'            => 'woo_onrefund_role',
					'type'          => 'select',
					'label'         => __( 'Assign this role to users when an order is refunded', 'um-woocommerce' ),
					'tooltip'       => __( 'Automatically set the user this role when an order is refunded.', 'um-woocommerce' ),
					'options'       => array( '' => __( 'None', 'um-woocommerce' ) ) + UM()->roles()->get_roles(),
					'placeholder'   => __( 'Community role...', 'um-woocommerce' ),
					'size'          => 'small',
				),
				array(
					'id'        => 'woo_remove_roles',
					'type'      => 'select',
					'label'     => __( 'Remove previous roles when change role on complete/processing or refund payment', 'um-woocommerce' ),
					'tooltip'   => __( 'If yes then remove all users roles and add current, else add current role to other roles, which user already has', 'um-woocommerce' ),
					'options'   => array( 0 => __( 'No', 'um-woocommerce' ), 1 => __( 'Yes', 'um-woocommerce' ) ),
					'size'      => 'small',
					//'conditional'   => array( 'woo_oncomplete_role||woo_onrefund_role', '!=', '' ),
				),
				array(
					'id'        => 'woo_hide_billing_tab_from_account',
					'type'      => 'checkbox',
					'label'     => __( 'Hide billing tab from members in account page', 'um-woocommerce' ),
					'tooltip'   => __( 'Enable this option If you do not want to show the billing tab from members in account page.', 'um-woocommerce' ),
				),
				array(
					'id'        => 'woo_hide_shipping_tab_from_account',
					'type'      => 'checkbox',
					'label'     => __( 'Hide shipping tab from members in account page', 'um-woocommerce' ),
					'tooltip'   => __( 'Enable this option If you do not want to show the shipping tab from members in account page.', 'um-woocommerce' ),
				),
				array(
					'id'        => 'woo_account_order_ations',
					'type'      => 'checkbox',
					'label'     => __( 'Show order actions', 'um-woocommerce' ),
					'tooltip'   => __( 'Show Actions column in the Orders table.', 'um-woocommerce' ),
				),
				array(
					'id'        => 'woo_hide_add_to_cart_button',
					'type'      => 'checkbox',
					'label'     => __( 'Hide "Add to cart" button', 'um-woocommerce' ),
					'tooltip'   => __( 'Enable this option If you do not want to show the "Add to cart" button for specific user roles or not logged in users.', 'um-woocommerce' ),
				),
				array(
					'id'            => 'woo_hide_add_to_cart_button_options',
					'type'          => 'select',
					'label'         => __( 'Hide "Add to cart" button for', 'um-woocommerce' ),
					'options'       => array(
											'0' => __( 'Everyone', 'ultimate-member' ),
											'1' => __( 'Logged out users', 'ultimate-member' ),
											'2' => __( 'Logged in users', 'ultimate-member' ),
									   ),
					'size'          => 'small',
					'conditional'   => array( 'woo_hide_add_to_cart_button', '=', 1 ),
				),
				array(
					'id'            => 'woo_hide_add_to_cart_button_roles',
					'type'          => 'select',
					'label'         => __( 'Hide "Add to cart" button for selected user roles', 'um-woocommerce' ),
					'options'       => UM()->roles()->get_roles(),
					'multi'         => true,
					'size'          => 'small',
					'conditional'   => array( 'woo_hide_add_to_cart_button_options', '=', 2 ),
				),
			);

			if ( UM()->WooCommerce_API()->api()->is_wc_subscription_plugin_active() ) {
				$fields[] = array(
					'id'        => 'woo_disable_subscriptions_switcher',
					'type'      => 'checkbox',
					'label'     => __( 'Disable subscriptions roles switcher', 'um-woocommerce' ),
					'tooltip'   => __( 'Disable default WooCommerce Subscriptions roles switcher.', 'um-woocommerce' ),
				);
			}

			$settings['extensions']['sections']['woocommerce'] = array(
				'title'  => __( 'Woocommerce', 'um-woocommerce' ),
				'fields' => $fields,
			);

			return $settings;
		}


		/**
		 *
		 */
		public function add_product_metabox() {
			add_meta_box(
				'um-admin-product-settings',
				__( 'Ultimate Member', 'um-woocommerce' ),
				array( &$this, 'load_metabox_product' ),
				'product',
				'normal',
				'default'
			);
		}


		/**
		 * @param $object
		 * @param $box
		 */
		public function load_metabox_product( $object, $box ) {
			global $post;

			$box['id'] = str_replace( 'um-admin-product-','', $box['id'] );

			preg_match('#\{.*?\}#s', $box['id'], $matches);

			if ( isset( $matches[0] ) ) {
				$path = $matches[0];
				$box['id'] = preg_replace('~(\\{[^}]+\\})~','', $box['id'] );
			} else {
				$path = um_woocommerce_path;
			}

			$path = str_replace('{','', $path );
			$path = str_replace('}','', $path );

			include_once $path . 'includes/admin/templates/product/'. $box['id'] . '.php';
		}


		/**
		 * Create role options
		 *
		 * @param $roles_metaboxes
		 *
		 * @return array
		 */
		public function add_role_metabox( $roles_metaboxes ) {
			$roles_metaboxes[] = array(
				'id'        => "um-admin-form-woocommerce{" . um_woocommerce_path . "}",
				'title'     => __( 'WooCommerce', 'um-woocommerce' ),
				'callback'  => array( UM()->metabox(), 'load_metabox_role' ),
				'screen'    => 'um_role_meta',
				'context'   => 'normal',
				'priority'  => 'default'
			);

			return $roles_metaboxes;
		}


		/**
		 * Add handler for save metabox fields content
		 *
		 * @param $post_id
		 * @param $post
		 *
		 * @return mixed
		 */
		public function save_metabox_product_settings( $post_id, $post ) {
			//make this handler only on product form submit
			if ( defined( 'DOING_AJAX' ) && DOING_AJAX ) {
				return $post_id;
			}

			if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
				return $post_id;
			}

			if ( empty( $_REQUEST['_wpnonce'] ) ) {
				return $post_id;
			}

			if ( empty( $post->post_type ) || 'product' != $post->post_type ) {
				return $post_id;
			}

			if ( ! empty( $_POST['_um_woo_product_role'] ) ) {
				update_post_meta( $post_id, '_um_woo_product_role', sanitize_text_field( $_POST['_um_woo_product_role'] ) );
			} else {
				delete_post_meta( $post_id, '_um_woo_product_role' );
			}

			if ( UM()->WooCommerce_API()->api()->is_wc_subscription_plugin_active() ) {
				if ( ! empty( $_POST['_um_woo_product_activated_role'] ) ) {
					update_post_meta( $post_id, '_um_woo_product_activated_role', sanitize_text_field( $_POST['_um_woo_product_activated_role'] ) );
				} else {
					delete_post_meta( $post_id, '_um_woo_product_activated_role' );
				}

				if ( ! empty( $_POST['_um_woo_product_downgrade_pending_role'] ) ) {
					update_post_meta( $post_id, '_um_woo_product_downgrade_pending_role', sanitize_text_field( $_POST['_um_woo_product_downgrade_pending_role'] ) );
				} else {
					delete_post_meta( $post_id, '_um_woo_product_downgrade_pending_role' );
				}

				if ( ! empty( $_POST['_um_woo_product_downgrade_onhold_role'] ) ) {
					update_post_meta( $post_id, '_um_woo_product_downgrade_onhold_role', sanitize_text_field( $_POST['_um_woo_product_downgrade_onhold_role'] ) );
				} else {
					delete_post_meta( $post_id, '_um_woo_product_downgrade_onhold_role' );
				}

				if ( ! empty( $_POST['_um_woo_product_downgrade_expired_role'] ) ) {
					update_post_meta( $post_id, '_um_woo_product_downgrade_expired_role', sanitize_text_field( $_POST['_um_woo_product_downgrade_expired_role'] ) );
				} else {
					delete_post_meta( $post_id, '_um_woo_product_downgrade_expired_role' );
				}

				if ( ! empty( $_POST['_um_woo_product_downgrade_cancelled_role'] ) ) {
					update_post_meta( $post_id, '_um_woo_product_downgrade_cancelled_role', sanitize_text_field( $_POST['_um_woo_product_downgrade_cancelled_role'] ) );
				} else {
					delete_post_meta( $post_id, '_um_woo_product_downgrade_cancelled_role' );
				}

				if ( ! empty( $_POST['_um_woo_product_downgrade_pendingcancel_role'] ) ) {
					update_post_meta( $post_id, '_um_woo_product_downgrade_pendingcancel_role', sanitize_text_field( $_POST['_um_woo_product_downgrade_pendingcancel_role'] ) );
				} else {
					delete_post_meta( $post_id, '_um_woo_product_downgrade_pendingcancel_role' );
				}
			}

			return $post_id;
		}


		/**
		 * Scan templates from extension
		 *
		 * @param $scan_files
		 *
		 * @return array
		 */
		public function um_woocommerce_extend_scan_files( $scan_files ) {
			$extension_files['um-woocommerce'] = UM()->admin_settings()->scan_template_files( um_woocommerce_path . '/templates/' );
			$scan_files                        = array_merge( $scan_files, $extension_files );

			return $scan_files;
		}


		/**
		 * Get template paths
		 *
		 * @param $located
		 * @param $file
		 *
		 * @return array
		 */
		public function um_woocommerce_get_path_template( $located, $file ) {
			if ( file_exists( get_stylesheet_directory() . '/ultimate-member/um-woocommerce/' . $file ) ) {
				$located = array(
					'theme' => get_stylesheet_directory() . '/ultimate-member/um-woocommerce/' . $file,
					'core'  => um_woocommerce_path . 'templates/' . $file,
				);
			}

			return $located;
		}
	}
}
