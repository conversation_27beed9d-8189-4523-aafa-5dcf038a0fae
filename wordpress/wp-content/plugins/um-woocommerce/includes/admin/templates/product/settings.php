<?php
/**
 * Product metabox "Ultimate Member"
 *
 * @var WP_Post $object
 * @var	WP_Post $post
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
if ( empty( $post ) ) {
	return;
}
$product_id = $post->ID;
?>

<div class="um-admin-metabox">
	<?php
	$roles_single        = array( '' => __( 'None', 'um-woocommerce' ) ) + UM()->roles()->get_roles();
	$roles_subscription  = array(
		''       => __( 'None', 'um-woocommerce' ),
		'ignore' => __( 'Ignore (don\'t change the role)', 'um-woocommerce' ),
	);
	$roles_subscription += UM()->roles()->get_roles();

	$um_woo_product_role = (string) get_post_meta( $product_id, '_um_woo_product_role', true );
	$um_woo_product_role = ( $um_woo_product_role ) ? $um_woo_product_role : '';

	$meta_fields = array(
		array(
			'id'      => '_um_woo_product_role',
			'type'    => 'select',
			'label'   => __( 'When this product is bought move user to this role', 'um-woocommerce' ),
			'value'   => $um_woo_product_role,
			'options' => $roles_single,
			'class'   => 'um-woo-not-subscription-settings',
		),
	);

	if ( UM()->WooCommerce_API()->api()->is_wc_subscription_plugin_active() ) {
		$um_woo_product_activated_role = (string) get_post_meta( $product_id, '_um_woo_product_activated_role', true );
		$um_woo_product_activated_role = ( $um_woo_product_activated_role ) ? $um_woo_product_activated_role : '';

		$meta_fields[] = array(
			'id'      => '_um_woo_product_activated_role',
			'type'    => 'select',
			'label'   => __( 'When subscription is ACTIVATED move user to this role', 'um-woocommerce' ),
			'value'   => $um_woo_product_activated_role,
			'options' => $roles_subscription,
			'class'   => 'um-woo-subscription-settings',
		);

		$um_woo_product_downgrade_pending_role = (string) get_post_meta( $product_id, '_um_woo_product_downgrade_pending_role', true );
		$um_woo_product_downgrade_pending_role = ( $um_woo_product_downgrade_pending_role ) ? $um_woo_product_downgrade_pending_role : '';

		$meta_fields[] = array(
			'id'      => '_um_woo_product_downgrade_pending_role',
			'type'    => 'select',
			'label'   => __( 'When subscription is PENDING move user to this role', 'um-woocommerce' ),
			'value'   => $um_woo_product_downgrade_pending_role,
			'options' => $roles_subscription,
			'class'   => 'um-woo-subscription-settings',
		);

		$um_woo_product_downgrade_onhold_role = (string) get_post_meta( $product_id, '_um_woo_product_downgrade_onhold_role', true );
		$um_woo_product_downgrade_onhold_role = ( $um_woo_product_downgrade_onhold_role ) ? $um_woo_product_downgrade_onhold_role : '';

		$meta_fields[] = array(
			'id'      => '_um_woo_product_downgrade_onhold_role',
			'type'    => 'select',
			'label'   => __( 'When subscription is ON-HOLD move user to this role', 'um-woocommerce' ),
			'value'   => $um_woo_product_downgrade_onhold_role,
			'options' => $roles_subscription,
			'class'   => 'um-woo-subscription-settings',
		);

		$um_woo_product_downgrade_expired_role = (string) get_post_meta( $product_id, '_um_woo_product_downgrade_expired_role', true );
		$um_woo_product_downgrade_expired_role = ( $um_woo_product_downgrade_expired_role ) ? $um_woo_product_downgrade_expired_role : '';

		$meta_fields[] = array(
			'id'      => '_um_woo_product_downgrade_expired_role',
			'type'    => 'select',
			'label'   => __( 'When subscription is EXPIRED move user to this role', 'um-woocommerce' ),
			'value'   => $um_woo_product_downgrade_expired_role,
			'options' => $roles_subscription,
			'class'   => 'um-woo-subscription-settings',
		);

		$um_woo_product_downgrade_cancelled_role = (string) get_post_meta( $product_id, '_um_woo_product_downgrade_cancelled_role', true );
		$um_woo_product_downgrade_cancelled_role = ( $um_woo_product_downgrade_cancelled_role ) ? $um_woo_product_downgrade_cancelled_role : '';

		$meta_fields[] = array(
			'id'      => '_um_woo_product_downgrade_cancelled_role',
			'type'    => 'select',
			'label'   => __( 'When subscription is CANCELLED move user to this role', 'um-woocommerce' ),
			'value'   => $um_woo_product_downgrade_cancelled_role,
			'options' => $roles_subscription,
			'class'   => 'um-woo-subscription-settings',
		);

		//pending-cancel
		$um_woo_product_downgrade_pendingcancel_role = (string) get_post_meta( $product_id, '_um_woo_product_downgrade_pendingcancel_role', true );
		$um_woo_product_downgrade_pendingcancel_role = ( $um_woo_product_downgrade_pendingcancel_role ) ? $um_woo_product_downgrade_pendingcancel_role : '';

		$meta_fields[] = array(
			'id'      => '_um_woo_product_downgrade_pendingcancel_role',
			'type'    => 'select',
			'label'   => __( 'When subscription is PENDING-CANCEL move user to this role', 'um-woocommerce' ),
			'value'   => $um_woo_product_downgrade_pendingcancel_role,
			'options' => $roles_subscription,
			'class'   => 'um-woo-subscription-settings',
		);
	}

	UM()->admin_forms(
		array(
			'class'     => 'um-wc-product-settings um-half-column',
			'prefix_id' => '',
			'fields'    => $meta_fields,
		)
	)->render_form();
	?>
	<div class="clear"></div>
</div>
