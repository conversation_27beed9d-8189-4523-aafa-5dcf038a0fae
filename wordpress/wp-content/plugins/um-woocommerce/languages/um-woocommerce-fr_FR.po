msgid ""
msgstr ""
"Project-Id-Version: Ultimate Member - WooCommerce\n"
"POT-Creation-Date: 2023-04-21 15:38-0400\n"
"PO-Revision-Date: 2023-04-21 15:41-0400\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: fr_FR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.2.2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: um-woocommerce.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"

#: includes/admin/core/class-admin.php:57
msgid "Woocommerce License Key"
msgstr "Clé de licence Woocommerce"

#: includes/admin/core/class-admin.php:67
msgid ""
"Ignore the roles update, if the user has these roles on complete/processing "
"or refund payment"
msgstr ""
"Ignorer la mise à jour des rôles, si l’utilisateur dispose de ces rôles sur "
"le paiement terminé/de traitement ou de remboursement"

#: includes/admin/core/class-admin.php:68
msgid ""
"Only applicable if you assigned a role when order is completed/processing or "
"refund."
msgstr ""
"Applicable uniquement si vous avez attribué un rôle lorsque la commande est "
"terminée / traitement ou remboursement."

#: includes/admin/core/class-admin.php:70
msgid "Community role(s).."
msgstr "Role(s) communautaire(s)"

#: includes/admin/core/class-admin.php:77
msgid "Assign this role to users when an order is completed/processing"
msgstr ""
"Attribuer ce rôle aux utilisateurs lorsqu’une commande est terminée/traitée"

#: includes/admin/core/class-admin.php:78
msgid ""
"Automatically set the user this role when an order's payment is completed."
msgstr ""
"Définissez automatiquement ce rôle pour l’utilisateur lorsque le paiement "
"d’une commande est terminé."

#: includes/admin/core/class-admin.php:79
#: includes/admin/core/class-admin.php:96
#: includes/admin/templates/product/settings.php:8
#: includes/admin/templates/product/settings.php:9
msgid "None"
msgstr "Aucun"

#: includes/admin/core/class-admin.php:80
#: includes/admin/core/class-admin.php:97
msgid "Community role..."
msgstr "Rôle communautaire"

#: includes/admin/core/class-admin.php:86
msgid ""
"Upgrade user role when payment is on-hold before complete or processing "
"status"
msgstr ""
"Mettre à niveau le rôle d'utilisateur lorsque le paiement est en attente "
"avant l'état complet ou de traitement"

#: includes/admin/core/class-admin.php:87
#: includes/admin/core/class-admin.php:105
msgid "No"
msgstr "Non"

#: includes/admin/core/class-admin.php:87
#: includes/admin/core/class-admin.php:105
msgid "Yes"
msgstr "Oui"

#: includes/admin/core/class-admin.php:94
msgid "Assign this role to users when an order is refunded"
msgstr "Attribuer ce rôle aux utilisateurs lorsqu’une commande est remboursée"

#: includes/admin/core/class-admin.php:95
msgid "Automatically set the user this role when an order is refunded."
msgstr ""
"Définissez automatiquement ce rôle à l’utilisateur lorsqu’une commande est "
"remboursée."

#: includes/admin/core/class-admin.php:103
msgid ""
"Remove previous roles when change role on complete/processing or refund "
"payment"
msgstr ""
"Supprimer les rôles précédents lors d’un changement de rôle lors du paiement "
"terminé/de traitement ou de remboursement"

#: includes/admin/core/class-admin.php:104
msgid ""
"If yes then remove all users roles and add current, else add current role to "
"other roles, which user already has"
msgstr ""
"Si oui, puis supprimer tous les rôles des utilisateurs et ajouter actuel, "
"sinon ajouter le rôle actuel à d'autres rôles, dont l'utilisateur a déjà"

#: includes/admin/core/class-admin.php:112
msgid "Hide billing tab from members in account page"
msgstr "Masquer l'onglet facturation pour les membres dans la page de compte"

#: includes/admin/core/class-admin.php:113
msgid ""
"Enable this option If you do not want to show the billing tab from members "
"in account page."
msgstr ""
"Activez cette option Si vous ne souhaitez pas afficher l'onglet de "
"facturation des membres dans la page de compte."

#: includes/admin/core/class-admin.php:118
msgid "Hide shipping tab from members in account page"
msgstr "Masquer l'onglet d'expédition dans la page de compte"

#: includes/admin/core/class-admin.php:119
msgid ""
"Enable this option If you do not want to show the shipping tab from members "
"in account page."
msgstr ""
"Activez cette option Si vous ne souhaitez pas afficher l'onglet Expédition "
"des membres dans la page de compte."

#: includes/admin/core/class-admin.php:124
msgid "Show order actions"
msgstr "Afficher les actions de commande"

#: includes/admin/core/class-admin.php:125
msgid "Show Actions column in the Orders table."
msgstr "Afficher la colonne Actions dans la table Ordres."

#: includes/admin/core/class-admin.php:130
msgid "Hide \"Add to cart\" button"
msgstr "Masquer le bouton d’ajout au panier"

#: includes/admin/core/class-admin.php:131
msgid ""
"Enable this option If you do not want to show the \"Add to cart\" button for "
"specific user roles or not logged in users."
msgstr ""
"Activez cette option si vous ne souhaitez pas afficher le bouton « Ajouter "
"au panier » pour des rôles d’utilisateur spécifiques ou des utilisateurs non "
"connectés."

#: includes/admin/core/class-admin.php:136
msgid "Hide \"Add to cart\" button for"
msgstr "Masquer le bouton « Ajouter au panier » pour"

#: includes/admin/core/class-admin.php:138
msgid "Everyone"
msgstr "Tout le monde"

#: includes/admin/core/class-admin.php:139
msgid "Logged out users"
msgstr "Visiteurs déconnectés"

#: includes/admin/core/class-admin.php:140
msgid "Logged in users"
msgstr "Utilisateurs connectés"

#: includes/admin/core/class-admin.php:148
msgid "Hide \"Add to cart\" button for selected user roles"
msgstr ""
"Masquer le bouton « Ajouter au panier » pour les rôles d’utilisateur "
"sélectionnés"

#: includes/admin/core/class-admin.php:160
msgid "Disable subscriptions roles switcher"
msgstr "Désactiver le sélecteur de rôles des abonnements"

#: includes/admin/core/class-admin.php:161
msgid "Disable default WooCommerce Subscriptions roles switcher."
msgstr "Désactivez le sélecteur de rôles WooCommerce Subscriptions par défaut."

#: includes/admin/core/class-admin.php:166
msgid "Woocommerce"
msgstr "WooCommerce"

#. Author of the plugin/theme
#: includes/admin/core/class-admin.php:180
msgid "Ultimate Member"
msgstr "Ultimate Member"

#: includes/admin/core/class-admin.php:224
msgid "WooCommerce"
msgstr "WooCommerce"

#: includes/admin/templates/product/settings.php:9
msgid "Ignore (don't change the role)"
msgstr "Ignorer (ne pas modifier le rôle)"

#: includes/admin/templates/product/settings.php:18
msgid "When this product is bought move user to this role"
msgstr "Quand ce produit est acheté, déplacer l'utilisateur vers ce rôle"

#: includes/admin/templates/product/settings.php:32
msgid "When subscription is ACTIVATED move user to this role"
msgstr "Lorsque l’abonnement est activé déplacer l'utilisateur vers ce rôle :"

#: includes/admin/templates/product/settings.php:44
msgid "When subscription is PENDING move user to this role"
msgstr "Lorsque l’abonnement est suspendu, déplacer l'utilisateur vers ce rôle"

#: includes/admin/templates/product/settings.php:56
msgid "When subscription is ON-HOLD move user to this role"
msgstr ""
"Lorsque l’abonnement est en attente, déplacer l'utilisateur vers ce rôle"

#: includes/admin/templates/product/settings.php:68
msgid "When subscription is EXPIRED move user to this role"
msgstr "Lorsque l’abonnement est expiré, déplacer l'utilisateur vers ce rôle :"

#: includes/admin/templates/product/settings.php:80
msgid "When subscription is CANCELLED move user to this role"
msgstr "Lorsque l’abonnement est annulé, déplacer l'utilisateur vers ce rôle :"

#: includes/admin/templates/product/settings.php:93
msgid "When subscription is PENDING-CANCEL move user to this role"
msgstr ""
"Lorsque l’abonnement est EN ATTENTE-ANNULÉE, déplacer l’utilisateur vers ce "
"rôle"

#: includes/admin/templates/role/woocommerce.php:11
msgid "Display purchases tab in profile?"
msgstr "Afficher l'onglet des achats sur le profil ?"

#: includes/admin/templates/role/woocommerce.php:17
msgid "Display reviews tab in profile?"
msgstr "Afficher l'onglet des évaluations sur le profil?"

#: includes/admin/templates/role/woocommerce.php:23
msgid "Display billing address under account?"
msgstr "Afficher l'adresse de facturation sur le compte ?"

#: includes/admin/templates/role/woocommerce.php:24
msgid ""
"Depends on option <em>Hide billing tab from members in account page</em> on "
"[Ultimate Member > Settings > Extensions > Woocommerce] page."
msgstr ""
"Dépend de l’option <em>Masquer l’onglet de facturation des membres dans la "
"page du compte sur la page</em> [Paramètres > > extensions du membre ultime "
"> Woocommerce]."

#: includes/admin/templates/role/woocommerce.php:30
msgid "Display shipping address under account?"
msgstr "Afficher l'adresse de livraison sur le compte?"

#: includes/admin/templates/role/woocommerce.php:31
msgid ""
"Depends on option <em>Hide shipping tab from members in account page</em> on "
"[Ultimate Member > Settings > Extensions > Woocommerce] page."
msgstr ""
"Dépend de l’option <em>Masquer l’onglet d’expédition des membres dans la "
"page du compte sur la page</em> [Paramètres > > extensions des membres "
"ultimes > Woocommerce]."

#: includes/admin/templates/role/woocommerce.php:37
msgid "Display orders under account?"
msgstr "Afficher l'onglet des commandes sur le compte ?"

#: includes/admin/templates/role/woocommerce.php:43
msgid "Display subscriptions under account?"
msgstr "Afficher les abonnements sous le compte ?"

#: includes/admin/templates/role/woocommerce.php:49
msgid "Display downloads under account?"
msgstr "Afficher les téléchargements sous le compte ?"

#: includes/admin/templates/role/woocommerce.php:55
msgid "Display payment methods under account?"
msgstr "Afficher les méthodes de paiement sous le compte ?"

#: includes/core/actions/um-woocommerce-checkout.php:25
msgid "You must be logged in to checkout."
msgstr "Vous devez être identifié pour commander."

#: includes/core/actions/um-woocommerce-checkout.php:25
msgid "New customer?"
msgstr "Nouveau client ?"

#: includes/core/actions/um-woocommerce-checkout.php:25
msgid "Click here to register"
msgstr "Cliquez ici pour vous enregistrer"

#: includes/core/actions/um-woocommerce-order.php:634
msgid "User role is changed."
msgstr "Le rôle de l’utilisateur est modifié."

#: includes/core/actions/um-woocommerce-order.php:637
msgid "User: "
msgstr "Utilisateur: "

#: includes/core/actions/um-woocommerce-order.php:640
msgid "New roles: "
msgstr "Nouveaux rôles : "

#: includes/core/actions/um-woocommerce-order.php:643
msgid "Old roles: "
msgstr "Anciens rôles : "

#: includes/core/actions/um-woocommerce-order.php:646
msgid "Action: "
msgstr "Action: "

#: includes/core/actions/um-woocommerce-order.php:649
msgid "Referer: "
msgstr "Référent : "

#: includes/core/actions/um-woocommerce-tabs.php:29
#: templates/my-purchases.php:18
msgid "You did not purchase any product yet."
msgstr "Vous n'avez acheté aucun produit encore."

#: includes/core/actions/um-woocommerce-tabs.php:29
#: templates/my-purchases.php:18
msgid "User did not purchase any product yet."
msgstr "L'utilisateur n'a encore acheté aucun produit."

#: includes/core/actions/um-woocommerce-tabs.php:63
#: templates/product-reviews.php:16
msgid "You did not review any products yet."
msgstr "Vous n’avez pas encore commenté ou noté de produit ou service."

#: includes/core/actions/um-woocommerce-tabs.php:63
#: templates/product-reviews.php:16
msgid "User did not review any product yet."
msgstr "L'utilisateur n'a encore évalué de produit."

#: includes/core/class-woocommerce-account.php:123
msgid "Billing Address"
msgstr "Adresse de facturation"

#: includes/core/class-woocommerce-account.php:126
#: includes/core/class-woocommerce-account.php:136
msgid "Save Address"
msgstr "Enregistrer l’adresse"

#: includes/core/class-woocommerce-account.php:133
msgid "Shipping Address"
msgstr "Adresse de livraison"

#: includes/core/class-woocommerce-account.php:143
msgid "My Orders"
msgstr "Mes commandes"

#: includes/core/class-woocommerce-account.php:152
msgid "Subscriptions"
msgstr "Abonnements"

#: includes/core/class-woocommerce-account.php:161
msgid "Downloads"
msgstr "Téléchargements"

#: includes/core/class-woocommerce-account.php:170
msgid "Payment methods"
msgstr "Moyens de paiement"

#: includes/core/class-woocommerce-account.php:184
msgid "Memberships"
msgstr "Membres associatifs"

#: includes/core/class-woocommerce-account.php:197
msgid "Wishlist"
msgstr "Liste de souhaits"

#: includes/core/class-woocommerce-account.php:209
msgid "Store Manager"
msgstr "Gérant de la boutique"

#: includes/core/class-woocommerce-account.php:226
msgid "Followings"
msgstr "Suivre"

#: includes/core/class-woocommerce-account.php:235
msgid "Inquiries"
msgstr "Demandes d'informations"

#: includes/core/class-woocommerce-account.php:244
msgid "Support Tickets"
msgstr "Tickets de support"

#: includes/core/class-woocommerce-account.php:380
#, php-format
msgid "\"%s\" field is required"
msgstr "\"%s\" est un champ requis."

#: includes/core/class-woocommerce-account.php:529
#: includes/core/class-woocommerce-account.php:802
msgid "PDF Invoice"
msgstr "Facture PDF"

#: includes/core/class-woocommerce-account.php:543
msgid "Cancel order"
msgstr "Annuler la commande"

#: includes/core/class-woocommerce-account.php:709
msgid "No User Memberships found"
msgstr "Aucune adhésion trouvée"

#: includes/core/class-woocommerce-account.php:1119
msgid "Your billing address is updated."
msgstr "Votre adresse de facturation est mise à jour."

#: includes/core/class-woocommerce-account.php:1123
msgid "Your shipping address is updated."
msgstr "Votre adresse de livraison est mise à jour."

#: includes/core/class-woocommerce-main-api.php:33
#: includes/core/class-woocommerce-main-api.php:36
msgid "WC Billing First name"
msgstr "Prénom de facturation"

#: includes/core/class-woocommerce-main-api.php:43
#: includes/core/class-woocommerce-main-api.php:46
msgid "WC Billing Last name"
msgstr "Nom de famille de facturation"

#: includes/core/class-woocommerce-main-api.php:53
#: includes/core/class-woocommerce-main-api.php:56
msgid "WC Billing Company"
msgstr "Société de facturation"

#: includes/core/class-woocommerce-main-api.php:63
#: includes/core/class-woocommerce-main-api.php:66
msgid "WC Billing Address 1"
msgstr "Adresse de facturation 1"

#: includes/core/class-woocommerce-main-api.php:73
#: includes/core/class-woocommerce-main-api.php:76
msgid "WC Billing Address 2"
msgstr "Adresse de facturation 2"

#: includes/core/class-woocommerce-main-api.php:83
#: includes/core/class-woocommerce-main-api.php:86
#: includes/core/class-woocommerce-member-directory.php:387
msgid "WC Billing city"
msgstr "Ville de facturation"

#: includes/core/class-woocommerce-main-api.php:93
#: includes/core/class-woocommerce-main-api.php:96
msgid "WC Billing postcode"
msgstr "Code postal de facturation"

#: includes/core/class-woocommerce-main-api.php:103
#: includes/core/class-woocommerce-main-api.php:106
#: includes/core/class-woocommerce-member-directory.php:388
msgid "WC Billing country"
msgstr "Pays de facturation"

#: includes/core/class-woocommerce-main-api.php:114
#: includes/core/class-woocommerce-main-api.php:117
#: includes/core/class-woocommerce-member-directory.php:394
msgid "WC Billing state"
msgstr "Pays"

#: includes/core/class-woocommerce-main-api.php:124
#: includes/core/class-woocommerce-main-api.php:127
msgid "WC Billing phone"
msgstr "Téléphone de facturation"

#: includes/core/class-woocommerce-main-api.php:134
#: includes/core/class-woocommerce-main-api.php:137
msgid "WC Billing email"
msgstr "Email de facturation"

#: includes/core/class-woocommerce-main-api.php:146
#: includes/core/class-woocommerce-main-api.php:149
msgid "WC Shipping First name"
msgstr "Expédition Prénom"

#: includes/core/class-woocommerce-main-api.php:156
#: includes/core/class-woocommerce-main-api.php:159
msgid "WC Shipping Last name"
msgstr "Expédition Nom de famille"

#: includes/core/class-woocommerce-main-api.php:166
#: includes/core/class-woocommerce-main-api.php:169
msgid "WC Shipping Company"
msgstr "WC Shipping Company"

#: includes/core/class-woocommerce-main-api.php:176
#: includes/core/class-woocommerce-main-api.php:179
msgid "WC Shipping Address 1"
msgstr "Adresse d'expédition 1"

#: includes/core/class-woocommerce-main-api.php:186
#: includes/core/class-woocommerce-main-api.php:189
msgid "WC Shipping Address 2"
msgstr "Adresse d'expédition 2"

#: includes/core/class-woocommerce-main-api.php:196
#: includes/core/class-woocommerce-main-api.php:199
#: includes/core/class-woocommerce-member-directory.php:389
msgid "WC Shipping city"
msgstr "Ville d'expédition"

#: includes/core/class-woocommerce-main-api.php:206
#: includes/core/class-woocommerce-main-api.php:209
msgid "WC Shipping postcode"
msgstr "Code postal d'expédition"

#: includes/core/class-woocommerce-main-api.php:216
#: includes/core/class-woocommerce-main-api.php:219
#: includes/core/class-woocommerce-member-directory.php:390
msgid "WC Shipping country"
msgstr "Pays d'expédition"

#: includes/core/class-woocommerce-main-api.php:227
#: includes/core/class-woocommerce-main-api.php:230
#: includes/core/class-woocommerce-member-directory.php:395
msgid "WC Shipping state"
msgstr "Pays"

#: includes/core/class-woocommerce-main-api.php:237
#: includes/core/class-woocommerce-main-api.php:240
msgid "WC Shipping phone"
msgstr "Téléphone d'expédition"

#: includes/core/class-woocommerce-main-api.php:247
#: includes/core/class-woocommerce-main-api.php:250
msgid "WC Shipping email"
msgstr "Expédition e-mail"

#: includes/core/class-woocommerce-main-api.php:436
msgid ""
"To enable automatic renewals for this subscription, you will first need to "
"add a payment method."
msgstr ""
"Pour activer le renouvellement automatique de cette formule, vous devez "
"d'abord ajouter un mode de paiement."

#: includes/core/class-woocommerce-main-api.php:436
msgid "Would you like to add a payment method now?"
msgstr "Voulez-vous ajouter un mode de paiement maintenant ?"

#: includes/core/class-woocommerce-main-api.php:484
msgid "Required"
msgstr "Obligatoire"

#: includes/core/class-woocommerce-main-api.php:508
msgid "Can not find state field in this form."
msgstr "Impossible de trouver le champ d’état dans ce formulaire."

#: includes/core/class-woocommerce-member-directory.php:391
#: includes/core/filters/um-woocommerce-fields.php:25
#: includes/core/filters/um-woocommerce-fields.php:28
msgid "Total Orders"
msgstr "Total des commandes"

#: includes/core/class-woocommerce-member-directory.php:392
#: includes/core/filters/um-woocommerce-fields.php:14
#: includes/core/filters/um-woocommerce-fields.php:17
msgid "Total Spent"
msgstr "Dépenses totales"

#: includes/core/class-woocommerce-member-directory.php:502
msgid "<strong>Total Orders:</strong>&nbsp;{value}"
msgstr "<strong>Nombre total de commandes :</strong> {value}"

#: includes/core/class-woocommerce-member-directory.php:503
msgid "<strong>Total Orders:</strong>&nbsp;{min_range} - {max_range}"
msgstr "<strong>Nombre total de commandes :</strong> {min_range} - {max_range}"

#: includes/core/class-woocommerce-member-directory.php:507
#: includes/core/class-woocommerce-member-directory.php:508
msgid "<strong>Total Spent ("
msgstr "<strong>Total dépensé (</strong>"

#: includes/core/filters/um-woocommerce-fields.php:66
#, php-format
msgid "%s order"
msgstr "%s commande"

#: includes/core/filters/um-woocommerce-fields.php:68
#, php-format
msgid "%s orders"
msgstr "%s commandes"

#: includes/core/filters/um-woocommerce-tabs.php:13
msgid "Purchases"
msgstr "Achats"

#: includes/core/filters/um-woocommerce-tabs.php:18
msgid "Product Reviews"
msgstr "Évaluations du produit"

#: templates/my-purchases.php:42 templates/product-reviews.php:35
#, php-format
msgid "<a href=\"%s\" class=\"um-woo-grid-imgc\">%s</a>"
msgstr "<a href=\"%s\" class=\"um-woo-grid-imgc\">%s</a>"

#: templates/my-purchases.php:44 templates/product-reviews.php:37
#, php-format
msgid "<img src=\"%s\" alt=\"%s\" class=\"um-woo-grid-imgc\" />"
msgstr "<img src=\"%s\" alt=\"%s\" class=\"um-woo-grid-imgc\" />"

#: templates/my-purchases.php:44 templates/product-reviews.php:37
msgid "Placeholder"
msgstr "Etiquette"

#: templates/my-purchases.php:53
msgid "Total Sales"
msgstr "Total des ventes"

#: templates/order-popup.php:22
#, php-format
msgid "Order# %s"
msgstr "Commande #%s"

#: templates/order-popup.php:31
#, php-format
msgid ""
"Order #<mark class=\"order-number\">%s</mark> was placed on <mark "
"class=\"order-date\">%s</mark> and is currently <mark class=\"order-"
"status\">%s</mark>."
msgstr ""
"La commande n°<mark class=\"order-number\">%s</mark> a été passée le <mark "
"class=\"order-date\">%s</mark> et est actuellement <mark class=\"order-"
"status\">%s</mark>."

#: templates/order-popup.php:35
msgid "Order Updates"
msgstr "Mises à jour de la commande"

#: templates/order-popup.php:41
msgid "l jS \\o\\f F Y, h:ia"
msgstr "l jS \\o\\f F Y, h:ia"

#: templates/orders.php:30 templates/orders.php:52
msgid "Date"
msgstr "Date"

#: templates/orders.php:31 templates/orders.php:55
msgid "Status"
msgstr "Statut"

#: templates/orders.php:32 templates/orders.php:58
msgid "Total"
msgstr "Total"

#: templates/orders.php:35
msgid "Actions"
msgstr "Actions"

#: templates/orders.php:60
msgid "View order"
msgstr "Voir la commande"

#: templates/orders.php:87
msgid "Jump to page:"
msgstr "Aller à la page:"

#: templates/orders.php:93
#, php-format
msgid "%s of %d"
msgstr "%s de %d"

#: templates/orders.php:103
msgid "First Page"
msgstr "Première page"

#: templates/orders.php:111
msgid "Previous"
msgstr "Précédent"

#: templates/orders.php:131
msgid "Next"
msgstr "Suivant"

#: templates/orders.php:139
msgid "Last Page"
msgstr "Dernière page"

#: templates/orders.php:149
msgid "You don't have orders yet"
msgstr "Vous n’avez pas encore de commande."

#: templates/subscription.php:15
msgid "All subscriptions"
msgstr "Tous les abonnements"

#: templates/subscription.php:16
msgid "Subscription"
msgstr "Adhésion"

#: um-woocommerce.php:46 um-woocommerce.php:62
#, php-format
msgid ""
"The <strong>%s</strong> extension requires the Ultimate Member plugin to be "
"activated to work properly. You can download it <a href=\"https://wordpress."
"org/plugins/ultimate-member\">here</a>"
msgstr ""
"L’extension <strong>%s</strong> nécessite que l’extension Ultimate Member "
"soit activé pour fonctionner correctement. Vous pouvez la télécharger <a "
"href=\"https://wordpress.org/plugins/ultimate-member\">ici</a>"

#: um-woocommerce.php:70
#, php-format
msgid "WooCommerce must be activated before you can use %s."
msgstr "WooCommerce doit être activé avant de pouvoir utiliser %s."

#. Plugin Name of the plugin/theme
msgid "Ultimate Member - WooCommerce"
msgstr "Membre ultime-WooCommerce"

#. Plugin URI of the plugin/theme
msgid "http://ultimatemember.com/extensions/woocommerce"
msgstr "http://ultimatemember.com/extensions/woocommerce"

#. Description of the plugin/theme
msgid "Integrates your WooCommerce store with Ultimate Member."
msgstr "Intègre votre boutique WooCommerce avec Ultimate member."

#. Author URI of the plugin/theme
msgid "http://ultimatemember.com/"
msgstr "http://ultimatemember.com/"
