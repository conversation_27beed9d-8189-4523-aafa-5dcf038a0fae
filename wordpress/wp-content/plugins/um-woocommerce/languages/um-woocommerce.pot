# Copyright (C) 2025 Ultimate Member
# This file is distributed under the same license as the Ultimate Member - WooCommerce plugin.
msgid ""
msgstr ""
"Project-Id-Version: Ultimate Member - WooCommerce 2.4.4\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/woocommerce\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-03-03T14:44:48+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: um-woocommerce\n"

#. Plugin Name of the plugin
#: um-woocommerce.php
msgid "Ultimate Member - WooCommerce"
msgstr ""

#. Plugin URI of the plugin
#: um-woocommerce.php
msgid "http://ultimatemember.com/extensions/woocommerce"
msgstr ""

#. Description of the plugin
#: um-woocommerce.php
msgid "Integrates your WooCommerce store with Ultimate Member."
msgstr ""

#. Author of the plugin
#: um-woocommerce.php
#: includes/admin/core/class-admin.php:209
msgid "Ultimate Member"
msgstr ""

#. Author URI of the plugin
#: um-woocommerce.php
msgid "http://ultimatemember.com/"
msgstr ""

#: includes/admin/core/class-admin.php:68
msgid "UM Profile:"
msgstr ""

#: includes/admin/core/class-admin.php:69
#: includes/core/filters/um-woocommerce-email.php:24
msgid "View profile &rarr;"
msgstr ""

#: includes/admin/core/class-admin.php:86
#: um-woocommerce.php:97
msgid "Woocommerce License Key"
msgstr ""

#: includes/admin/core/class-admin.php:96
msgid "Ignore the roles update, if the user has these roles on complete/processing or refund payment"
msgstr ""

#: includes/admin/core/class-admin.php:97
msgid "Only applicable if you assigned a role when order is completed/processing or refund."
msgstr ""

#: includes/admin/core/class-admin.php:99
msgid "Community role(s).."
msgstr ""

#: includes/admin/core/class-admin.php:106
msgid "Assign this role to users when an order is completed/processing"
msgstr ""

#: includes/admin/core/class-admin.php:107
msgid "Automatically set the user this role when an order's payment is completed."
msgstr ""

#: includes/admin/core/class-admin.php:108
#: includes/admin/core/class-admin.php:125
#: includes/admin/templates/product/settings.php:19
#: includes/admin/templates/product/settings.php:21
msgid "None"
msgstr ""

#: includes/admin/core/class-admin.php:109
#: includes/admin/core/class-admin.php:126
msgid "Community role..."
msgstr ""

#: includes/admin/core/class-admin.php:115
msgid "Upgrade user role when payment is on-hold before complete or processing status"
msgstr ""

#: includes/admin/core/class-admin.php:116
#: includes/admin/core/class-admin.php:134
msgid "No"
msgstr ""

#: includes/admin/core/class-admin.php:116
#: includes/admin/core/class-admin.php:134
msgid "Yes"
msgstr ""

#: includes/admin/core/class-admin.php:123
msgid "Assign this role to users when an order is refunded"
msgstr ""

#: includes/admin/core/class-admin.php:124
msgid "Automatically set the user this role when an order is refunded."
msgstr ""

#: includes/admin/core/class-admin.php:132
msgid "Remove previous roles when change role on complete/processing or refund payment"
msgstr ""

#: includes/admin/core/class-admin.php:133
msgid "If yes then remove all users roles and add current, else add current role to other roles, which user already has"
msgstr ""

#: includes/admin/core/class-admin.php:141
msgid "Hide billing tab from members in account page"
msgstr ""

#: includes/admin/core/class-admin.php:142
msgid "Enable this option If you do not want to show the billing tab from members in account page."
msgstr ""

#: includes/admin/core/class-admin.php:147
msgid "Hide shipping tab from members in account page"
msgstr ""

#: includes/admin/core/class-admin.php:148
msgid "Enable this option If you do not want to show the shipping tab from members in account page."
msgstr ""

#: includes/admin/core/class-admin.php:153
msgid "Show order actions"
msgstr ""

#: includes/admin/core/class-admin.php:154
msgid "Show Actions column in the Orders table."
msgstr ""

#: includes/admin/core/class-admin.php:159
msgid "Hide \"Add to cart\" button"
msgstr ""

#: includes/admin/core/class-admin.php:160
msgid "Enable this option If you do not want to show the \"Add to cart\" button for specific user roles or not logged in users."
msgstr ""

#: includes/admin/core/class-admin.php:165
msgid "Hide \"Add to cart\" button for"
msgstr ""

#: includes/admin/core/class-admin.php:177
msgid "Hide \"Add to cart\" button for selected user roles"
msgstr ""

#: includes/admin/core/class-admin.php:189
msgid "Disable subscriptions roles switcher"
msgstr ""

#: includes/admin/core/class-admin.php:190
msgid "Disable default WooCommerce Subscriptions roles switcher."
msgstr ""

#: includes/admin/core/class-admin.php:195
msgid "Woocommerce"
msgstr ""

#: includes/admin/core/class-admin.php:253
msgid "WooCommerce"
msgstr ""

#: includes/admin/templates/product/settings.php:22
msgid "Ignore (don't change the role)"
msgstr ""

#: includes/admin/templates/product/settings.php:33
msgid "When this product is bought move user to this role"
msgstr ""

#: includes/admin/templates/product/settings.php:47
msgid "When subscription is ACTIVATED move user to this role"
msgstr ""

#: includes/admin/templates/product/settings.php:59
msgid "When subscription is PENDING move user to this role"
msgstr ""

#: includes/admin/templates/product/settings.php:71
msgid "When subscription is ON-HOLD move user to this role"
msgstr ""

#: includes/admin/templates/product/settings.php:83
msgid "When subscription is EXPIRED move user to this role"
msgstr ""

#: includes/admin/templates/product/settings.php:95
msgid "When subscription is CANCELLED move user to this role"
msgstr ""

#: includes/admin/templates/product/settings.php:108
msgid "When subscription is PENDING-CANCEL move user to this role"
msgstr ""

#: includes/admin/templates/role/woocommerce.php:18
msgid "Display purchases tab in profile?"
msgstr ""

#: includes/admin/templates/role/woocommerce.php:24
msgid "Display reviews tab in profile?"
msgstr ""

#: includes/admin/templates/role/woocommerce.php:30
msgid "Display billing address under account?"
msgstr ""

#: includes/admin/templates/role/woocommerce.php:31
msgid "Depends on option <em>Hide billing tab from members in account page</em> on [Ultimate Member > Settings > Extensions > Woocommerce] page."
msgstr ""

#: includes/admin/templates/role/woocommerce.php:37
msgid "Display shipping address under account?"
msgstr ""

#: includes/admin/templates/role/woocommerce.php:38
msgid "Depends on option <em>Hide shipping tab from members in account page</em> on [Ultimate Member > Settings > Extensions > Woocommerce] page."
msgstr ""

#: includes/admin/templates/role/woocommerce.php:44
msgid "Display orders under account?"
msgstr ""

#: includes/admin/templates/role/woocommerce.php:50
msgid "Display subscriptions under account?"
msgstr ""

#: includes/admin/templates/role/woocommerce.php:56
msgid "Display downloads under account?"
msgstr ""

#: includes/admin/templates/role/woocommerce.php:62
msgid "Display payment methods under account?"
msgstr ""

#: includes/core/actions/um-woocommerce-checkout.php:25
msgid "New customer?"
msgstr ""

#: includes/core/actions/um-woocommerce-checkout.php:25
msgid "Click here to register"
msgstr ""

#: includes/core/actions/um-woocommerce-order.php:634
msgid "User role is changed."
msgstr ""

#: includes/core/actions/um-woocommerce-order.php:637
msgid "User: "
msgstr ""

#: includes/core/actions/um-woocommerce-order.php:640
msgid "New roles: "
msgstr ""

#: includes/core/actions/um-woocommerce-order.php:643
msgid "Old roles: "
msgstr ""

#: includes/core/actions/um-woocommerce-order.php:646
msgid "Action: "
msgstr ""

#: includes/core/actions/um-woocommerce-order.php:649
msgid "Referer: "
msgstr ""

#: includes/core/actions/um-woocommerce-tabs.php:29
#: templates/my-purchases.php:20
msgid "You did not purchase any product yet."
msgstr ""

#: includes/core/actions/um-woocommerce-tabs.php:29
#: templates/my-purchases.php:20
msgid "User did not purchase any product yet."
msgstr ""

#: includes/core/actions/um-woocommerce-tabs.php:63
#: templates/product-reviews.php:18
msgid "You did not review any products yet."
msgstr ""

#: includes/core/actions/um-woocommerce-tabs.php:63
#: templates/product-reviews.php:18
msgid "User did not review any product yet."
msgstr ""

#: includes/core/class-woocommerce-access.php:129
msgid "Login"
msgstr ""

#: includes/core/class-woocommerce-access.php:132
msgid "Info"
msgstr ""

#: includes/core/class-woocommerce-access.php:135
msgid "Home"
msgstr ""

#: includes/core/class-woocommerce-account.php:155
msgid "Billing Address"
msgstr ""

#: includes/core/class-woocommerce-account.php:159
#: includes/core/class-woocommerce-account.php:170
msgid "Save Address"
msgstr ""

#: includes/core/class-woocommerce-account.php:166
msgid "Shipping Address"
msgstr ""

#: includes/core/class-woocommerce-account.php:177
msgid "My Orders"
msgstr ""

#: includes/core/class-woocommerce-account.php:187
msgid "Subscriptions"
msgstr ""

#: includes/core/class-woocommerce-account.php:197
msgid "Downloads"
msgstr ""

#: includes/core/class-woocommerce-account.php:207
msgid "Payment methods"
msgstr ""

#: includes/core/class-woocommerce-account.php:222
msgid "Memberships"
msgstr ""

#: includes/core/class-woocommerce-account.php:236
msgid "Wishlist"
msgstr ""

#: includes/core/class-woocommerce-account.php:249
msgid "Store Manager"
msgstr ""

#: includes/core/class-woocommerce-account.php:266
msgid "Followings"
msgstr ""

#: includes/core/class-woocommerce-account.php:275
msgid "Inquiries"
msgstr ""

#: includes/core/class-woocommerce-account.php:284
msgid "Support Tickets"
msgstr ""

#. translators: %s is a field label
#: includes/core/class-woocommerce-account.php:438
msgid "\"%s\" field is required"
msgstr ""

#: includes/core/class-woocommerce-account.php:702
msgid "No User Memberships found"
msgstr ""

#: includes/core/class-woocommerce-account.php:937
#: includes/core/class-woocommerce-main-api.php:583
msgid "PDF Invoice"
msgstr ""

#: includes/core/class-woocommerce-account.php:971
msgid "Cancel order"
msgstr ""

#: includes/core/class-woocommerce-account.php:1120
msgid "Your billing address is updated."
msgstr ""

#: includes/core/class-woocommerce-account.php:1124
msgid "Your shipping address is updated."
msgstr ""

#: includes/core/class-woocommerce-main-api.php:39
#: includes/core/class-woocommerce-main-api.php:42
msgid "WC Billing First name"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:49
#: includes/core/class-woocommerce-main-api.php:52
msgid "WC Billing Last name"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:59
#: includes/core/class-woocommerce-main-api.php:62
msgid "WC Billing Company"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:69
#: includes/core/class-woocommerce-main-api.php:72
msgid "WC Billing Address 1"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:79
#: includes/core/class-woocommerce-main-api.php:82
msgid "WC Billing Address 2"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:89
#: includes/core/class-woocommerce-main-api.php:92
#: includes/core/class-woocommerce-member-directory.php:426
msgid "WC Billing city"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:99
#: includes/core/class-woocommerce-main-api.php:102
msgid "WC Billing postcode"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:109
#: includes/core/class-woocommerce-main-api.php:112
#: includes/core/class-woocommerce-member-directory.php:427
msgid "WC Billing country"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:120
#: includes/core/class-woocommerce-main-api.php:123
#: includes/core/class-woocommerce-member-directory.php:432
msgid "WC Billing state"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:130
#: includes/core/class-woocommerce-main-api.php:133
msgid "WC Billing phone"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:140
#: includes/core/class-woocommerce-main-api.php:143
msgid "WC Billing email"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:151
#: includes/core/class-woocommerce-main-api.php:154
msgid "WC Shipping First name"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:161
#: includes/core/class-woocommerce-main-api.php:164
msgid "WC Shipping Last name"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:171
#: includes/core/class-woocommerce-main-api.php:174
msgid "WC Shipping Company"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:181
#: includes/core/class-woocommerce-main-api.php:184
msgid "WC Shipping Address 1"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:191
#: includes/core/class-woocommerce-main-api.php:194
msgid "WC Shipping Address 2"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:201
#: includes/core/class-woocommerce-main-api.php:204
#: includes/core/class-woocommerce-member-directory.php:428
msgid "WC Shipping city"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:211
#: includes/core/class-woocommerce-main-api.php:214
msgid "WC Shipping postcode"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:221
#: includes/core/class-woocommerce-main-api.php:224
#: includes/core/class-woocommerce-member-directory.php:429
msgid "WC Shipping country"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:232
#: includes/core/class-woocommerce-main-api.php:235
#: includes/core/class-woocommerce-member-directory.php:433
msgid "WC Shipping state"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:242
#: includes/core/class-woocommerce-main-api.php:245
msgid "WC Shipping phone"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:252
#: includes/core/class-woocommerce-main-api.php:255
msgid "WC Shipping email"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:462
msgid "To enable automatic renewals for this subscription, you will first need to add a payment method."
msgstr ""

#: includes/core/class-woocommerce-main-api.php:462
msgid "Would you like to add a payment method now?"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:515
msgid "Required"
msgstr ""

#: includes/core/class-woocommerce-main-api.php:542
msgid "Can not find state field in this form."
msgstr ""

#: includes/core/class-woocommerce-member-directory.php:430
#: includes/core/filters/um-woocommerce-fields.php:29
#: includes/core/filters/um-woocommerce-fields.php:32
msgid "Total Orders"
msgstr ""

#: includes/core/class-woocommerce-member-directory.php:431
#: includes/core/filters/um-woocommerce-fields.php:18
#: includes/core/filters/um-woocommerce-fields.php:21
msgid "Total Spent"
msgstr ""

#: includes/core/class-woocommerce-member-directory.php:557
msgid "Total Orders:"
msgstr ""

#. translators: %s means the Woocommerce symbol.
#: includes/core/class-woocommerce-member-directory.php:560
msgid "Total Spent (%s):"
msgstr ""

#: includes/core/filters/um-woocommerce-fields.php:99
msgid "%s order"
msgid_plural "%s orders"
msgstr[0] ""
msgstr[1] ""

#: includes/core/filters/um-woocommerce-tabs.php:13
msgid "Purchases"
msgstr ""

#: includes/core/filters/um-woocommerce-tabs.php:18
msgid "Product Reviews"
msgstr ""

#. translators: %1$s is a product link, %2$s is a product image
#: templates/my-purchases.php:45
#: templates/product-reviews.php:38
msgid "<a href=\"%1$s\" class=\"um-woo-grid-imgc\">%2$s</a>"
msgstr ""

#. translators: %1$s is a product image link, %2$s is a placeholder text
#: templates/my-purchases.php:48
#: templates/product-reviews.php:41
msgid "<img src=\"%1$s\" alt=\"%2$s\" class=\"um-woo-grid-imgc\" />"
msgstr ""

#. translators: %1$s is a product image link, %2$s is a placeholder text
#: templates/my-purchases.php:48
#: templates/product-reviews.php:41
msgid "Placeholder"
msgstr ""

#: templates/my-purchases.php:57
msgid "Total Sales"
msgstr ""

#. translators: %s is an order ID
#: templates/order-popup.php:26
msgid "Order# %s"
msgstr ""

#. translators: %1$s is an order ID, %2$s is an order date %3$s is an order status
#: templates/order-popup.php:35
msgid "Order #<mark class=\"order-number\">%1$s</mark> was placed on <mark class=\"order-date\">%2$s</mark> and is currently <mark class=\"order-status\">%3$s</mark>."
msgstr ""

#: templates/order-popup.php:39
msgid "Order Updates"
msgstr ""

#: templates/order-popup.php:45
msgid "l jS \\o\\f F Y, h:ia"
msgstr ""

#: templates/orders.php:40
#: templates/orders.php:67
msgid "Date"
msgstr ""

#: templates/orders.php:41
#: templates/orders.php:70
msgid "Status"
msgstr ""

#: templates/orders.php:42
#: templates/orders.php:73
msgid "Total"
msgstr ""

#: templates/orders.php:45
msgid "Actions"
msgstr ""

#: templates/orders.php:75
msgid "View order"
msgstr ""

#: templates/orders.php:102
msgid "Jump to page:"
msgstr ""

#. translators: %1$s is a current pagination pages, %2$s is a total pages for orders list
#: templates/orders.php:109
msgid "%1$s of %2$s"
msgstr ""

#: templates/orders.php:119
msgid "First Page"
msgstr ""

#: templates/orders.php:127
msgid "Previous"
msgstr ""

#: templates/orders.php:147
msgid "Next"
msgstr ""

#: templates/orders.php:155
msgid "Last Page"
msgstr ""

#: templates/orders.php:165
msgid "You don't have orders yet"
msgstr ""

#: templates/subscription.php:17
msgid "All subscriptions"
msgstr ""

#: templates/subscription.php:18
msgid "Subscription"
msgstr ""

#. translators: %s is the Woocommerce extension name.
#: um-woocommerce.php:55
#: um-woocommerce.php:72
msgid "The <strong>%s</strong> extension requires the Ultimate Member plugin to be activated to work properly. You can download it <a href=\"https://wordpress.org/plugins/ultimate-member\">here</a>"
msgstr ""

#. translators: %s is the Woocommerce extension name.
#: um-woocommerce.php:81
msgid "WooCommerce must be activated before you can use %s."
msgstr ""
