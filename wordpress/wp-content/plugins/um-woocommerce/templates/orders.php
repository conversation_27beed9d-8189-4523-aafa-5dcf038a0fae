<?php
/**
 * Template for the list of orders
 * Used on the "Account" page in the "My Orders" tab
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-woocommerce/orders.php
 *
 * @version 2.3.8
 *
 * @var array  $args
 * @var array  $customer_orders
 * @var string $date_time_format
 * @var int    $orders_page
 * @var int    $orders_per_page
 * @var int    $pages_to_show
 * @var int    $total_pages
 * @var string $url
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$order = '';
?>

<div class="um-woo-form um-woo-orders woocommerce">

	<?php
	if ( UM()->account()->current_tab === 'orders' && function_exists( 'wc_print_notices' ) ) {
		wc_print_notices();
	}
	?>

	<?php if( $total_pages ) { ?>

		<table class="shop_table shop_table_responsive my_account_orders">
			<thead>
				<tr>
					<?php do_action( 'um_woocommerce_orders_tab_before_table_header_row', $order, $customer_orders ); ?>
					<th class="order-date"><span class="nobr"><?php _e( 'Date', 'um-woocommerce' ); ?></span></th>
					<th class="order-status"><span class="nobr"><?php _e( 'Status', 'um-woocommerce' ); ?></span></th>
					<th class="order-total"><span class="nobr"><?php _e( 'Total', 'um-woocommerce' ); ?></span></th>
					<th class="order-detail">&nbsp;</th>
					<?php if ( UM()->options()->get('woo_account_order_ations') ) : ?>
					<th class="order-actions"><?php _e( 'Actions', 'um-woocommerce' ); ?></th>
					<?php endif; ?>
					<?php do_action( 'um_woocommerce_orders_tab_after_table_header_row', $order, $customer_orders ); ?>
				</tr>
			</thead>

			<tbody>
				<?php
				foreach ( $customer_orders as $order ) {
					if ( is_a( $order, 'WC_Order' ) ) {
						$order_id = $order->get_id();
					} elseif ( is_numeric( $order ) ) {
						$order_id = $order;
						$order    = wc_get_order( $order_id );
					} else {
						$order_id = $order->ID;
						$order    = wc_get_order( $order_id );
					}
					?>

					<tr class="order" data-order_id="<?php echo esc_attr( $order_id ); ?>">
						<?php do_action( 'um_woocommerce_orders_tab_before_table_row', $order, $customer_orders ); ?>
						<td class="order-date" data-title="<?php _e( 'Date', 'um-woocommerce' ); ?>">
							<time datetime="<?php echo $order->get_date_created()->date_i18n( 'Y-m-d' ); ?>"><?php echo $order->get_date_created()->date_i18n( $date_time_format ); ?></time>
						</td>
						<td class="order-status" data-title="<?php _e( 'Status', 'um-woocommerce' ); ?>">
							<span class="um-woo-status <?php echo $order->get_status(); ?>"><?php echo wc_get_order_status_name( $order->get_status() ); ?></span>
						</td>
						<td class="order-total" data-title="<?php _e( 'Total', 'um-woocommerce' ); ?>"><?php echo $order->get_formatted_order_total() ?></td>
						<td class="order-detail">
							<a href="<?php echo esc_url( "$url#$order_id" ); ?>" class="um-woo-view-order um-tip-n" title="<?php esc_attr_e( 'View order', 'um-woocommerce' ); ?>"><i class="um-icon-eye"></i></a>
							<?php do_action( 'um_woocommerce_orders_tab_actions', $order, $customer_orders ); ?>
						</td>
						<?php
						if ( UM()->options()->get('woo_account_order_ations') ) {
							$actions = wc_get_account_orders_actions( $order );
							echo '<td class="order-actions">';
							if ( !empty( $actions ) ) {
								foreach ( $actions as $key => $action ) {
									echo '<a href="' . esc_url( $action['url'] ) . '" class="button ' . sanitize_html_class( $key ) . '">' . esc_html( $action['name'] ) . '</a>';
								}
							}
							echo '</td>';
						}
						?>
						<?php do_action( 'um_woocommerce_orders_tab_after_table_row', $order, $customer_orders ); ?>
					</tr>

				<?php } ?>
			</tbody>
		</table>

		<?php if( $total_pages > 1 ) : ?>

			<div class="um-members-pagidrop uimob340-show uimob500-show">

				<?php
				_e( 'Jump to page:', 'um-woocommerce' );

				if( $pages_to_show <= $total_pages ) {
					?>
					<select onChange="window.location.href = this.value" class="um-s2" style="width: 100px">
						<?php for( $i = 1; $i <= $pages_to_show; $i++ ) { ?>
							<?php // translators: %1$s is a current pagination pages, %2$s is a total pages for orders list ?>
							<option value="<?php echo '?orders_page=' . esc_attr( $i ); ?>" <?php selected( $i, $orders_page ); ?>><?php printf( __( '%1$s of %2$s', 'um-woocommerce' ), $i, $total_pages ); ?></option>
						<?php } ?>
					</select>
				<?php } ?>

			</div>

			<div class="um-members-pagi uimob340-hide uimob500-hide">

				<?php if( $orders_page != 1 ) { ?>
					<a href="<?php echo '?orders_page=1'; ?>" class="pagi pagi-arrow um-tip-n" title="<?php _e( 'First Page', 'um-woocommerce' ); ?>"><i class="um-faicon-angle-double-left"></i></a>
				<?php } else { ?>
					<span class="pagi pagi-arrow disabled"><i class="um-faicon-angle-double-left"></i></span>
					<?php
				}

				if( $orders_page > 1 ) {
					?>
					<a href="<?php echo '?orders_page=' . ( $orders_page - 1 ); ?>" class="pagi pagi-arrow um-tip-n" title="<?php _e( 'Previous', 'um-woocommerce' ); ?>"><i class="um-faicon-angle-left"></i></a>
				<?php } else { ?>
					<span class="pagi pagi-arrow disabled"><i class="um-faicon-angle-left"></i></span>
					<?php
				}

				if( $pages_to_show ) {
					for( $i = 1; $i <= $pages_to_show; $i++ ) {
						if( $orders_page == $i ) {
							?>
							<span class="pagi current"><?php echo esc_html( $i ); ?></span>
						<?php } else { ?>
							<a href="<?php echo '?orders_page=' . esc_attr( $i ); ?>" class="pagi"><?php echo esc_html( $i ); ?></a>
							<?php
						}
					}
				}

				if( $orders_page != $total_pages ) {
					?>
					<a href="<?php echo '?orders_page=' . ( esc_html( $orders_page ) + 1 ); ?>" class="pagi pagi-arrow um-tip-n" title="<?php _e( 'Next', 'um-woocommerce' ); ?>"><i class="um-faicon-angle-right"></i></a>
				<?php } else { ?>
					<span class="pagi pagi-arrow disabled"><i class="um-faicon-angle-right"></i></span>
					<?php
				}

				if( $orders_page != $total_pages ) {
					?>
					<a href="<?php echo '?orders_page=' . ( esc_attr( $total_pages ) ); ?>" class="pagi pagi-arrow um-tip-n" title="<?php _e( 'Last Page', 'um-woocommerce' ); ?>"><i class="um-faicon-angle-double-right"></i></a>
				<?php } else { ?>
					<span class="pagi pagi-arrow disabled"><i class="um-faicon-angle-double-right"></i></span>
				<?php } ?>

			</div>

		<?php endif; ?>

	<?php } else { ?>
		<div class="um-field"><?php _e( 'You don\'t have orders yet', 'um-woocommerce' ); ?></div>
	<?php } ?>

</div>
