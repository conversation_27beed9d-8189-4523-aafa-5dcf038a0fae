<?php
/**
 * Template for the "View order" popup
 * Used on the "Profile" page, "My Orders" tab
 * Called from the WooCommerce_Main_API->ajax_get_order() method
 * @version 2.3.4
 *
 * This template can be overridden by copying it to yourtheme/ultimate-member/um-woocommerce/order-popup.php
 * @var int   $order_id
 * @var array $notes
 */
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
?>

<div class="um-woo-order-head um-popup-header">

	<div class="um-woo-customer">
		<?php echo get_avatar( get_current_user_id(), 34 ); ?>
		<span><?php echo esc_html( um_user( 'display_name' ) ); ?></span>
	</div>

	<div class="um-woo-orderid">
		<?php // translators: %s is an order ID ?>
		<?php printf( __( 'Order# %s', 'um-woocommerce' ), $order_id ); ?>
		<a href="#" class="um-woo-order-hide"><i class="um-icon-close"></i></a>
	</div>

	<div class="um-clear"></div>
</div>

<div class="um-woo-order-body um-popup-autogrow2">
	<?php // translators: %1$s is an order ID, %2$s is an order date %3$s is an order status ?>
	<p class="order-info"><?php printf( __( 'Order #<mark class="order-number">%1$s</mark> was placed on <mark class="order-date">%2$s</mark> and is currently <mark class="order-status">%3$s</mark>.', 'um-woocommerce' ), $order->get_order_number(), date_i18n( get_option( 'date_format' ), strtotime( $order->get_date_created() ) ), wc_get_order_status_name( $order->get_status() ) ); ?></p>

	<?php if( $notes ) : ?>

		<h2><?php _e( 'Order Updates', 'um-woocommerce' ); ?></h2>
		<ol class="commentlist notes">
			<?php foreach( $notes as $note ) : ?>
				<li class="comment note">
					<div class="comment_container">
						<div class="comment-text">
							<p class="meta"><?php echo date_i18n( __( 'l jS \o\f F Y, h:ia', 'um-woocommerce' ), strtotime( $note->comment_date ) ); ?></p>
							<div class="description">
								<?php echo wpautop( wptexturize( $note->comment_content ) ); ?>
							</div>
							<div class="clear"></div>
						</div>
						<div class="clear"></div>
					</div>
				</li>
			<?php endforeach; ?>
		</ol>

		<?php
	endif;

	do_action( 'woocommerce_view_order', $order_id );
	?>

</div>

<div class="um-popup-footer" style="height:30px"></div>
