.awdr_free_product_text{
    display: inline-block;
    padding: 0px 10px;
    background-color: #3d9cd2;
    color: #fff;
    border-radius: 3px;
}
.variation-wdr_free_product{
    display: none !important;
}
.awdr_change_product{
    cursor: pointer;
}
.awdr_free_product_variants{
    padding: 5px;
}
.awdr-product-name{
    padding-left: 10px;
}
.awdr_change_product{
    display: flex;
}
.awdr-select-free-variant-product-toggle {
    color: gray;
    cursor: pointer;
    width: 100%;
    border: none;
    text-align: left;
    outline: none;
    font-size: 1.02em;
    transition: 0.4s;
}

.awdr-select-free-variant-product-toggle-active, .awdr-select-free-variant-product-toggle:hover {
    color: #444;
}

.awdr-select-variant-product {
    padding: 0 18px;
    display: none;
    background-color: white;
    overflow: hidden;
}
.awdr-select-free-variant-product-toggle:after {
    content: '\02795'; /* Unicode character for "plus" sign (+) */
    font-size: 12px;
    color: #777;
    margin-left: 10px;
}

.awdr-select-free-variant-product-toggle-active:after {
    content: "\2796";/* Unicode character for "minus" sign (-) */
}