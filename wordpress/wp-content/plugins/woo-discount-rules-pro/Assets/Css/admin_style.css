.awdr-get-y-middle-section{
    width: auto;
    padding-left: 10px;
}
.awdr-example{
    padding-left: 10px;
}
.awdr-buyx-gety-cumulative-radio-mode{
    display: flex;
}
.awdr-buyx-gety-cumulative-radio-mode span{
    color: #000000;
    padding-right: 10px;
    font-weight: bolder;
}
.awdr-set-sort-icon{
    color:darkgray;
    padding-bottom: 32px !important;
}
.awdr-set-sort-icon:hover{
    color: black;
}
.set-remove-icon{
    padding-bottom: 32px !important;
}
.awdr_bogo_main, .set_range_setter_group{
    cursor: move;
}
.awdr-bygy-cat-products{
    padding-top: 24px !important;
}
.awdr-bygy-all{
    padding-top: 16px !important;
}
.wdr-licence-invalid-text{
    color: #ffc100;
}
.wdr-licence-valid-text{
    color: #5cb85c;
}
.bxgy-icon{
    float:left;
}