# Copyright (C) 2025 Flycart
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: Discount Rules PRO 2.0 2.6.10\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/woo-discount-rules-pro\n"
"Last-Translator: Flycart <<EMAIL>>\n"
"Language-Team: Flycart <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-06-23T10:37:51+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.10.0\n"
"X-Domain: woo-discount-rules-pro\n"

#. Plugin Name of the plugin
#: woo-discount-rules-pro.php
msgid "Discount Rules PRO 2.0"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
#: woo-discount-rules-pro.php
msgid "https://www.flycart.org"
msgstr ""

#. Description of the plugin
#: woo-discount-rules-pro.php
msgid "PRO package for Discount Rules. You need both the Core and PRO packages to get the PRO features running."
msgstr ""

#. Author of the plugin
#: woo-discount-rules-pro.php
msgid "Flycart"
msgstr ""

#: App/Conditions/BillingCity.php:15
#: App/Conditions/ShippingCity.php:15
msgid "City"
msgstr ""

#: App/Conditions/BillingCity.php:16
msgid "Billing"
msgstr ""

#: App/Conditions/CartCoupon.php:15
msgid "Coupons"
msgstr ""

#: App/Conditions/CartCoupon.php:16
#: App/Conditions/CartItemsQuantity.php:19
#: App/Conditions/CartItemsWeight.php:17
#: App/Conditions/CartPaymentMethod.php:16
msgid "Cart"
msgstr ""

#: App/Conditions/CartItemcategoryCombination.php:20
msgid "Category combination"
msgstr ""

#: App/Conditions/CartItemcategoryCombination.php:21
#: App/Conditions/CartItemProductAttributes.php:16
#: App/Conditions/CartItemProductCategory.php:16
#: App/Conditions/CartItemProductCombination.php:20
#: App/Conditions/CartItemProductOnsale.php:17
#: App/Conditions/CartItemProducts.php:16
#: App/Conditions/CartItemProductSku.php:16
#: App/Conditions/CartItemProductTags.php:16
msgid "Cart Items"
msgstr ""

#: App/Conditions/CartItemProductAttributes.php:15
#: App/Controllers/Admin/Filters.php:37
msgid "Attributes"
msgstr ""

#: App/Conditions/CartItemProductCategory.php:15
#: App/Controllers/Admin/Filters.php:32
msgid "Category"
msgstr ""

#: App/Conditions/CartItemProductCombination.php:19
msgid "Product combination"
msgstr ""

#: App/Conditions/CartItemProductOnsale.php:16
#: App/Controllers/Admin/Filters.php:52
msgid "On sale products"
msgstr ""

#: App/Conditions/CartItemProducts.php:15
msgid "Products"
msgstr ""

#: App/Conditions/CartItemProductSku.php:15
msgid "SKU"
msgstr ""

#: App/Conditions/CartItemProductTags.php:15
msgid "Product Tags"
msgstr ""

#: App/Conditions/CartItemsQuantity.php:18
msgid "Item quantity"
msgstr ""

#: App/Conditions/CartItemsWeight.php:16
msgid "Total weight"
msgstr ""

#: App/Conditions/CartPaymentMethod.php:15
msgid "Payment Method"
msgstr ""

#: App/Conditions/OrderDate.php:15
msgid "Date"
msgstr ""

#: App/Conditions/OrderDate.php:16
#: App/Conditions/OrderDateAndTime.php:16
#: App/Conditions/OrderDays.php:16
#: App/Conditions/OrderTime.php:16
msgid "Date & Time"
msgstr ""

#: App/Conditions/OrderDateAndTime.php:15
msgid "Date and Time"
msgstr ""

#: App/Conditions/OrderDays.php:15
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:27
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:43
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:43
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:22
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:22
msgid "Days"
msgstr ""

#: App/Conditions/OrderTime.php:15
msgid "Time"
msgstr ""

#: App/Conditions/PurchaseFirstOrder.php:19
msgid "First order"
msgstr ""

#: App/Conditions/PurchaseFirstOrder.php:20
#: App/Conditions/PurchaseLastOrder.php:18
#: App/Conditions/PurchaseLastOrderAmount.php:18
#: App/Conditions/PurchasePreviousOrders.php:18
#: App/Conditions/PurchasePreviousOrdersForSpecificProduct.php:21
#: App/Conditions/PurchaseQuantitiesForSpecificProduct.php:21
#: App/Conditions/PurchaseSpent.php:18
msgid "Purchase History"
msgstr ""

#: App/Conditions/PurchaseLastOrder.php:17
msgid "Last order"
msgstr ""

#: App/Conditions/PurchaseLastOrderAmount.php:17
msgid "Last order amount"
msgstr ""

#: App/Conditions/PurchasePreviousOrders.php:17
msgid "Number of orders made"
msgstr ""

#: App/Conditions/PurchasePreviousOrdersForSpecificProduct.php:20
msgid "Number of orders made with following products"
msgstr ""

#: App/Conditions/PurchaseQuantitiesForSpecificProduct.php:20
msgid "Number of quantities made with following products"
msgstr ""

#: App/Conditions/PurchaseSpent.php:17
msgid "Total spent"
msgstr ""

#: App/Conditions/ShippingCity.php:16
#: App/Conditions/ShippingCountry.php:16
#: App/Conditions/ShippingState.php:16
#: App/Conditions/ShippingZipCode.php:16
msgid "Shipping"
msgstr ""

#: App/Conditions/ShippingCountry.php:15
msgid "Country"
msgstr ""

#: App/Conditions/ShippingState.php:15
msgid "State"
msgstr ""

#: App/Conditions/ShippingZipCode.php:15
msgid "Zipcode"
msgstr ""

#: App/Conditions/UserEmail.php:15
msgid "Email"
msgstr ""

#: App/Conditions/UserEmail.php:16
#: App/Conditions/UserList.php:16
#: App/Conditions/UserLoggedIn.php:16
#: App/Conditions/UserRole.php:16
msgid "Customer"
msgstr ""

#: App/Conditions/UserList.php:15
msgid "User"
msgstr ""

#: App/Conditions/UserLoggedIn.php:15
msgid "Is logged in"
msgstr ""

#: App/Conditions/UserRole.php:15
msgid "User role"
msgstr ""

#: App/Controllers/Admin/BuyXGetX.php:33
msgid "Buy X get X"
msgstr ""

#: App/Controllers/Admin/BuyXGetX.php:34
#: App/Controllers/Admin/BuyXGetY.php:34
msgid "Bogo Discount"
msgstr ""

#: App/Controllers/Admin/BuyXGetY.php:33
msgid "Buy X get Y"
msgstr ""

#: App/Controllers/Admin/Conditions.php:53
#: App/Controllers/Admin/Filters.php:61
msgid "Custom Taxonomy"
msgstr ""

#: App/Controllers/Admin/Filters.php:33
#: App/Controllers/Admin/Filters.php:38
#: App/Controllers/Admin/Filters.php:43
#: App/Controllers/Admin/Filters.php:48
#: App/Controllers/Admin/Filters.php:53
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:89
msgid "Product"
msgstr ""

#: App/Controllers/Admin/Filters.php:42
msgid "Tags"
msgstr ""

#: App/Controllers/Admin/Filters.php:47
msgid "SKUs"
msgstr ""

#: App/Controllers/Admin/FreeShipping.php:29
msgid "Free Shipping"
msgstr ""

#: App/Controllers/Admin/FreeShipping.php:30
msgid "Simple Discount"
msgstr ""

#: App/Controllers/Admin/GeneralHooks.php:44
#: App/Views/Admin/Settings/general.php:15
msgid "Validate"
msgstr ""

#: App/Controllers/Admin/GeneralHooks.php:45
msgid "Validating please wait.."
msgstr ""

#: App/Controllers/Admin/Set.php:33
msgid "Bundle (Set) Discount"
msgstr ""

#: App/Controllers/Admin/Set.php:34
msgid "Bulk Discount"
msgstr ""

#: App/Controllers/Admin/UpdateHandler.php:101
msgid "Please enter a valid license key"
msgstr ""

#: App/Controllers/Admin/UpdateHandler.php:104
msgid "License key check : Passed."
msgstr ""

#: App/Controllers/Admin/UpdateHandler.php:106
msgid "License key seems to be Invalid. Please enter a valid license key"
msgstr ""

#: App/Controllers/Admin/UpdateHandler.php:232
msgid "License key for the %s seems invalid. %s, you can get it from %s"
msgstr ""

#: App/Controllers/Admin/UpdateHandler.php:236
msgid "License key for the %s is not entered. %s, you can get it from %s"
msgstr ""

#: App/Helpers/FreeShippingMethod.php:24
msgid "Free shipping"
msgstr ""

#: App/Helpers/FreeShippingMethod.php:25
msgid "Free shipping is a special method which can be triggered with coupons and minimum spends."
msgstr ""

#: App/Helpers/FreeShippingMethod.php:28
msgid "Wdr Free Shipping"
msgstr ""

#: App/Rules/BOGO.php:168
msgid "Discount"
msgstr ""

#: App/Rules/BOGO.php:169
#: App/Views/Admin/Discounts/buy-x-get-x-range.php:50
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:165
msgid "Free"
msgstr ""

#: App/Rules/BuyXGetY.php:746
msgid "The product %s is invalid."
msgstr ""

#: App/Rules/BuyXGetY.php:761
msgid "The product %s is not purchasable (please check product configuration)."
msgstr ""

#: App/Views/Admin/Conditions/Billing/city.php:11
#: App/Views/Admin/Conditions/Cart/payment-method.php:14
#: App/Views/Admin/Conditions/DateTime/days.php:12
#: App/Views/Admin/Conditions/Products/product-attributes.php:14
#: App/Views/Admin/Conditions/Products/product-categories.php:13
#: App/Views/Admin/Conditions/Products/product-sku.php:14
#: App/Views/Admin/Conditions/Products/product-tags.php:14
#: App/Views/Admin/Conditions/Products/product-taxonomy.php:31
#: App/Views/Admin/Conditions/Products/products.php:14
#: App/Views/Admin/Conditions/Shipping/city.php:11
#: App/Views/Admin/Conditions/Shipping/country.php:12
#: App/Views/Admin/Conditions/Shipping/state.php:14
#: App/Views/Admin/Conditions/Shipping/zip-code.php:11
#: App/Views/Admin/Filters/attributes.php:10
#: App/Views/Admin/Filters/category.php:10
#: App/Views/Admin/Filters/common_edit.php:13
#: App/Views/Admin/Filters/common_edit.php:117
#: App/Views/Admin/Filters/sku.php:10
#: App/Views/Admin/Filters/tags.php:10
msgid "In List"
msgstr ""

#: App/Views/Admin/Conditions/Billing/city.php:12
#: App/Views/Admin/Conditions/Cart/payment-method.php:15
#: App/Views/Admin/Conditions/DateTime/days.php:13
#: App/Views/Admin/Conditions/Products/product-attributes.php:15
#: App/Views/Admin/Conditions/Products/product-categories.php:14
#: App/Views/Admin/Conditions/Products/product-sku.php:15
#: App/Views/Admin/Conditions/Products/product-tags.php:15
#: App/Views/Admin/Conditions/Products/product-taxonomy.php:32
#: App/Views/Admin/Conditions/Products/products.php:15
#: App/Views/Admin/Conditions/Shipping/city.php:12
#: App/Views/Admin/Conditions/Shipping/country.php:13
#: App/Views/Admin/Conditions/Shipping/state.php:15
#: App/Views/Admin/Conditions/Shipping/zip-code.php:12
#: App/Views/Admin/Filters/attributes.php:11
#: App/Views/Admin/Filters/category.php:11
#: App/Views/Admin/Filters/common_edit.php:14
#: App/Views/Admin/Filters/common_edit.php:118
#: App/Views/Admin/Filters/sku.php:11
#: App/Views/Admin/Filters/tags.php:11
msgid "Not In List"
msgstr ""

#: App/Views/Admin/Conditions/Billing/city.php:14
#: App/Views/Admin/Conditions/Shipping/city.php:14
msgid "Cities should be"
msgstr ""

#: App/Views/Admin/Conditions/Billing/city.php:20
#: App/Views/Admin/Conditions/Shipping/city.php:20
msgid "Enter Cities "
msgstr ""

#: App/Views/Admin/Conditions/Billing/city.php:22
#: App/Views/Admin/Conditions/Shipping/city.php:22
msgid "Example : Chicago, Houston"
msgstr ""

#: App/Views/Admin/Conditions/Cart/cart-quantity.php:12
#: App/Views/Admin/Conditions/Cart/weight.php:13
#: App/Views/Admin/Conditions/Products/category-combination.php:58
#: App/Views/Admin/Conditions/Products/product-attributes.php:48
#: App/Views/Admin/Conditions/Products/product-categories.php:49
#: App/Views/Admin/Conditions/Products/product-combination.php:41
#: App/Views/Admin/Conditions/Products/product-sku.php:41
#: App/Views/Admin/Conditions/Products/product-tags.php:45
#: App/Views/Admin/Conditions/Products/product-taxonomy.php:68
#: App/Views/Admin/Conditions/Products/products.php:42
#: App/Views/Admin/Conditions/PurchaseHistory/last-order-amount.php:12
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:119
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:119
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:98
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:98
msgid "Less than ( &lt; )"
msgstr ""

#: App/Views/Admin/Conditions/Cart/cart-quantity.php:13
#: App/Views/Admin/Conditions/Cart/weight.php:14
#: App/Views/Admin/Conditions/Products/category-combination.php:59
#: App/Views/Admin/Conditions/Products/product-attributes.php:47
#: App/Views/Admin/Conditions/Products/product-categories.php:48
#: App/Views/Admin/Conditions/Products/product-combination.php:42
#: App/Views/Admin/Conditions/Products/product-sku.php:40
#: App/Views/Admin/Conditions/Products/product-tags.php:44
#: App/Views/Admin/Conditions/Products/product-taxonomy.php:67
#: App/Views/Admin/Conditions/Products/products.php:41
#: App/Views/Admin/Conditions/PurchaseHistory/last-order-amount.php:13
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:120
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:120
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:99
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:99
msgid "Less than or equal ( &lt;= )"
msgstr ""

#: App/Views/Admin/Conditions/Cart/cart-quantity.php:14
#: App/Views/Admin/Conditions/Cart/weight.php:15
#: App/Views/Admin/Conditions/Products/category-combination.php:60
#: App/Views/Admin/Conditions/Products/product-attributes.php:49
#: App/Views/Admin/Conditions/Products/product-categories.php:50
#: App/Views/Admin/Conditions/Products/product-combination.php:43
#: App/Views/Admin/Conditions/Products/product-sku.php:42
#: App/Views/Admin/Conditions/Products/product-tags.php:46
#: App/Views/Admin/Conditions/Products/product-taxonomy.php:69
#: App/Views/Admin/Conditions/Products/products.php:43
#: App/Views/Admin/Conditions/PurchaseHistory/last-order-amount.php:14
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:121
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:121
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:100
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:100
msgid "Greater than or equal ( &gt;= )"
msgstr ""

#: App/Views/Admin/Conditions/Cart/cart-quantity.php:15
#: App/Views/Admin/Conditions/Cart/weight.php:16
#: App/Views/Admin/Conditions/Products/category-combination.php:61
#: App/Views/Admin/Conditions/Products/product-attributes.php:50
#: App/Views/Admin/Conditions/Products/product-categories.php:51
#: App/Views/Admin/Conditions/Products/product-combination.php:44
#: App/Views/Admin/Conditions/Products/product-sku.php:43
#: App/Views/Admin/Conditions/Products/product-tags.php:47
#: App/Views/Admin/Conditions/Products/product-taxonomy.php:70
#: App/Views/Admin/Conditions/Products/products.php:44
#: App/Views/Admin/Conditions/PurchaseHistory/last-order-amount.php:15
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:122
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:122
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:101
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:101
msgid "Greater than ( &gt; )"
msgstr ""

#: App/Views/Admin/Conditions/Cart/cart-quantity.php:17
msgid "Cart item quantity should be"
msgstr ""

#: App/Views/Admin/Conditions/Cart/cart-quantity.php:23
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:130
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:130
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:109
msgid "1"
msgstr ""

#: App/Views/Admin/Conditions/Cart/cart-quantity.php:24
msgid "Cart item quantity"
msgstr ""

#: App/Views/Admin/Conditions/Cart/cart-quantity.php:28
msgid "Count all items in cart"
msgstr ""

#: App/Views/Admin/Conditions/Cart/cart-quantity.php:29
msgid "Only count items chosen in the filters set for this rule"
msgstr ""

#: App/Views/Admin/Conditions/Cart/cart-quantity.php:31
msgid "How to calculate the item quantity"
msgstr ""

#: App/Views/Admin/Conditions/Cart/coupon.php:17
msgid "Create your own coupon "
msgstr ""

#: App/Views/Admin/Conditions/Cart/coupon.php:18
msgid "Apply if any one coupon is applied (Select from Woocommerce)"
msgstr ""

#: App/Views/Admin/Conditions/Cart/coupon.php:19
msgid "Apply if all coupon is applied (Select from Woocommerce)"
msgstr ""

#: App/Views/Admin/Conditions/Cart/coupon.php:21
msgid "select coupon by"
msgstr ""

#: App/Views/Admin/Conditions/Cart/coupon.php:28
msgid "Search Coupon"
msgstr ""

#: App/Views/Admin/Conditions/Cart/coupon.php:40
msgid "Select coupon"
msgstr ""

#: App/Views/Admin/Conditions/Cart/coupon.php:47
msgid "Coupon Name"
msgstr ""

#: App/Views/Admin/Conditions/Cart/coupon.php:50
msgid "Enter Coupon name"
msgstr ""

#: App/Views/Admin/Conditions/Cart/coupon.php:74
#: App/Views/Admin/Conditions/Cart/coupon.php:86
#: App/Views/Admin/Conditions/Cart/coupon.php:108
msgid "Copy URL"
msgstr ""

#: App/Views/Admin/Conditions/Cart/payment-method.php:17
msgid "Select by"
msgstr ""

#: App/Views/Admin/Conditions/Cart/payment-method.php:25
msgid "Search Payment Method"
msgstr ""

#: App/Views/Admin/Conditions/Cart/payment-method.php:40
msgid "Select payment method"
msgstr ""

#: App/Views/Admin/Conditions/Cart/weight.php:18
msgid "Weight should be"
msgstr ""

#: App/Views/Admin/Conditions/Cart/weight.php:23
#: App/Views/Admin/Conditions/PurchaseHistory/last-order-amount.php:23
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:109
msgid "0.00"
msgstr ""

#: App/Views/Admin/Conditions/Cart/weight.php:24
msgid "Weight"
msgstr ""

#: App/Views/Admin/Conditions/Customer/is-logged-in.php:11
#: App/Views/Admin/Conditions/PurchaseHistory/first-order.php:11
#: App/Views/Admin/Settings/cart.php:26
#: App/Views/Admin/Settings/promotion.php:16
msgid "Yes"
msgstr ""

#: App/Views/Admin/Conditions/Customer/is-logged-in.php:12
#: App/Views/Admin/Conditions/PurchaseHistory/first-order.php:12
#: App/Views/Admin/Settings/cart.php:31
#: App/Views/Admin/Settings/promotion.php:21
msgid "No"
msgstr ""

#: App/Views/Admin/Conditions/Customer/is-logged-in.php:14
msgid "Customer log in status"
msgstr ""

#: App/Views/Admin/Conditions/Customer/user-email.php:12
msgid "TLD (Eg:edu)"
msgstr ""

#: App/Views/Admin/Conditions/Customer/user-email.php:13
msgid "Domain (Eg:gmail.com)"
msgstr ""

#: App/Views/Admin/Conditions/Customer/user-email.php:15
msgid "Email should be"
msgstr ""

#: App/Views/Admin/Conditions/Customer/user-email.php:21
msgid "Enter values "
msgstr ""

#: App/Views/Admin/Conditions/Customer/user-email.php:22
msgid "Example : edu, org"
msgstr ""

#: App/Views/Admin/Conditions/Customer/user-email.php:23
msgid "Example : gmail.com, yahoo.com"
msgstr ""

#: App/Views/Admin/Conditions/Customer/user-role.php:14
#: App/Views/Admin/Conditions/Customer/user.php:12
msgid "in list"
msgstr ""

#: App/Views/Admin/Conditions/Customer/user-role.php:15
#: App/Views/Admin/Conditions/Customer/user.php:13
msgid "not in list"
msgstr ""

#: App/Views/Admin/Conditions/Customer/user-role.php:17
msgid "user role should be"
msgstr ""

#: App/Views/Admin/Conditions/Customer/user-role.php:25
msgid "Search User Roles"
msgstr ""

#: App/Views/Admin/Conditions/Customer/user-role.php:40
msgid "Select User Roles"
msgstr ""

#: App/Views/Admin/Conditions/Customer/user.php:15
msgid "User should be"
msgstr ""

#: App/Views/Admin/Conditions/Customer/user.php:23
msgid "Search User"
msgstr ""

#: App/Views/Admin/Conditions/Customer/user.php:37
msgid "Select User"
msgstr ""

#: App/Views/Admin/Conditions/DateTime/date-and-time.php:12
#: App/Views/Admin/Conditions/DateTime/date.php:11
msgid "From date"
msgstr ""

#: App/Views/Admin/Conditions/DateTime/date-and-time.php:15
#: App/Views/Admin/Conditions/DateTime/date.php:14
msgid "select date from"
msgstr ""

#: App/Views/Admin/Conditions/DateTime/date-and-time.php:21
#: App/Views/Admin/Conditions/DateTime/date.php:20
msgid "To date"
msgstr ""

#: App/Views/Admin/Conditions/DateTime/date-and-time.php:23
msgid "select date to"
msgstr ""

#: App/Views/Admin/Conditions/DateTime/date.php:23
msgid "select date to "
msgstr ""

#: App/Views/Admin/Conditions/DateTime/days.php:15
msgid "Day should be"
msgstr ""

#: App/Views/Admin/Conditions/DateTime/days.php:23
msgid "Search Days"
msgstr ""

#: App/Views/Admin/Conditions/DateTime/days.php:43
msgid "Select Days"
msgstr ""

#: App/Views/Admin/Conditions/DateTime/time.php:10
msgid "From"
msgstr ""

#: App/Views/Admin/Conditions/DateTime/time.php:13
msgid "Time From"
msgstr ""

#: App/Views/Admin/Conditions/DateTime/time.php:17
#: App/Views/Admin/Conditions/Products/category-combination.php:79
#: App/Views/Admin/Conditions/Products/product-combination.php:62
msgid "To"
msgstr ""

#: App/Views/Admin/Conditions/DateTime/time.php:20
msgid "Time To"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:14
#: App/Views/Admin/Conditions/Products/product-combination.php:13
msgid "Each"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:15
#: App/Views/Admin/Conditions/Products/product-combination.php:14
msgid "Combine"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:16
#: App/Views/Admin/Conditions/Products/product-combination.php:15
msgid "Any"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:18
#: App/Views/Admin/Conditions/Products/product-combination.php:17
msgid "Combination type"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:23
msgid "Search Category"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:46
msgid "Select category"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:50
#: App/Views/Admin/Conditions/Products/product-combination.php:55
#: App/Views/Admin/Conditions/Products/product-combination.php:56
#: App/Views/Admin/Conditions/Products/product-combination.php:63
#: App/Views/Admin/Discounts/buy-x-get-x-range.php:17
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:25
#: App/Views/Admin/Discounts/set-range.php:18
msgid "Quantity"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:51
msgid "Sub Total"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:52
msgid "Line Item Count"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:54
msgid "Select Value"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:62
#: App/Views/Admin/Conditions/Products/product-combination.php:45
msgid "Equal to ( = )"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:63
#: App/Views/Admin/Conditions/Products/product-combination.php:46
msgid "Not equal to ( != )"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:64
#: App/Views/Admin/Conditions/Products/product-combination.php:47
msgid "In range"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:66
#: App/Views/Admin/Conditions/Products/product-combination.php:49
msgid "Comparison should be"
msgstr ""

#: App/Views/Admin/Conditions/Products/category-combination.php:72
#: App/Views/Admin/Conditions/Products/category-combination.php:73
#: App/Views/Admin/Conditions/Products/category-combination.php:80
#: App/Views/Admin/Discounts/buy-x-get-x-range.php:59
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:176
#: App/Views/Admin/Discounts/set-range.php:35
msgid "Value"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-attributes.php:17
msgid "Attributes should be"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-attributes.php:24
msgid "Search Attributes"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-attributes.php:43
#: App/Views/Admin/Filters/attributes.php:19
msgid "Select Attributes"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-attributes.php:52
msgid "Attributes Quantity in cart"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-attributes.php:55
#: App/Views/Admin/Conditions/Products/product-categories.php:56
#: App/Views/Admin/Conditions/Products/product-sku.php:48
#: App/Views/Admin/Conditions/Products/product-tags.php:52
#: App/Views/Admin/Conditions/Products/product-taxonomy.php:75
#: App/Views/Admin/Conditions/Products/products.php:49
msgid "qty"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-attributes.php:59
msgid "Attributes Quantity"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-categories.php:16
msgid "categories should be"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-categories.php:23
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:98
msgid "Search Categories"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-categories.php:44
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:126
msgid "Select categories"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-categories.php:53
msgid "categories Quantity in cart"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-categories.php:60
#: App/Views/Admin/Conditions/Products/product-taxonomy.php:79
msgid "Category Quantity"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-combination.php:22
msgid "Search product"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-combination.php:37
msgid "Select product"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-onsale.php:11
#: App/Views/Admin/Filters/common_edit.php:107
#: App/Views/Admin/Filters/on-sale.php:11
msgid "Include"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-onsale.php:12
#: App/Views/Admin/Filters/common_edit.php:108
#: App/Views/Admin/Filters/on-sale.php:10
msgid "Exclude"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-onsale.php:14
#: App/Views/Admin/Conditions/Products/products.php:17
msgid "Products should be"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-sku.php:17
msgid "SKU should be"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-sku.php:24
msgid "Search SKUs"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-sku.php:36
#: App/Views/Admin/Filters/sku.php:18
msgid "Select SKUs"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-sku.php:45
msgid "SKUs Quantity In Cart"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-sku.php:52
msgid "SKUs Quantity"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-tags.php:17
msgid "tags should be"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-tags.php:24
msgid "Search Tags"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-tags.php:40
#: App/Views/Admin/Filters/tags.php:19
msgid "Select Tags"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-tags.php:49
msgid "Tags Quantity In Cart"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-tags.php:56
msgid "Tags Quantity"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-taxonomy.php:34
msgid "Taxonomy should be"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-taxonomy.php:43
msgid "Search Taxonomies"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-taxonomy.php:63
msgid "Select Taxonomies"
msgstr ""

#: App/Views/Admin/Conditions/Products/product-taxonomy.php:72
msgid "Categories Quantity In Cart"
msgstr ""

#: App/Views/Admin/Conditions/Products/products.php:22
msgid "Search Products"
msgstr ""

#: App/Views/Admin/Conditions/Products/products.php:37
msgid "Select Products"
msgstr ""

#: App/Views/Admin/Conditions/Products/products.php:46
msgid "Products Quantity In Cart"
msgstr ""

#: App/Views/Admin/Conditions/Products/products.php:53
msgid "Product Quantity"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/first-order.php:14
msgid "is first order?"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order-amount.php:17
msgid "order amount should be"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order-amount.php:24
msgid "order amount"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order-amount.php:29
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:103
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:139
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:139
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:116
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:117
msgid "Search Order Status"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order-amount.php:50
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:123
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:159
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:159
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:136
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:137
msgid "order status"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:13
msgid "within past"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:14
msgid "earlier than"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:16
msgid "order should be"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:21
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:37
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:37
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:16
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:16
msgid "Current"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:22
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:38
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:38
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:17
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:17
msgid "current day"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:23
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:39
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:39
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:18
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:18
msgid "current week"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:24
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:40
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:40
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:19
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:19
msgid "current month"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:25
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:41
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:41
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:20
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:20
msgid "current year"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:29
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:45
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:45
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:24
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:24
msgid "day"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:31
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:33
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:35
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:37
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:39
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:47
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:49
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:51
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:53
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:55
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:47
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:49
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:51
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:53
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:55
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:26
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:28
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:30
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:32
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:34
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:26
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:28
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:30
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:32
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:34
msgid "days"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:41
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:57
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:57
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:36
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:36
msgid "Weeks"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:43
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:59
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:59
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:38
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:38
msgid "week"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:45
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:47
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:49
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:61
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:63
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:65
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:61
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:63
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:65
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:40
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:42
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:44
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:40
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:42
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:44
msgid "weeks"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:51
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:67
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:67
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:46
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:46
msgid "Months"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:53
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:69
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:69
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:48
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:48
msgid "month"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:55
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:57
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:59
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:61
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:63
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:65
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:67
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:69
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:71
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:73
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:75
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:71
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:73
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:75
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:77
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:79
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:81
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:83
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:85
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:87
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:89
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:91
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:71
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:73
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:75
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:77
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:79
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:81
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:83
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:85
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:87
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:89
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:91
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:50
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:52
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:54
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:56
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:58
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:60
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:62
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:64
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:66
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:68
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:70
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:50
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:52
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:54
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:56
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:58
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:60
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:62
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:64
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:66
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:68
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:70
msgid "months"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:77
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:93
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:93
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:72
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:72
msgid "Years"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:79
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:81
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:83
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:85
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:87
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:89
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:91
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:93
#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:95
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:95
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:97
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:99
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:101
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:103
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:105
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:107
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:109
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:111
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:95
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:97
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:99
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:101
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:103
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:105
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:107
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:109
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:111
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:74
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:76
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:78
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:80
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:82
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:84
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:86
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:88
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:90
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:74
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:76
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:78
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:80
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:82
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:84
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:86
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:88
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:90
msgid "years"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/last-order.php:98
msgid "order time"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:16
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:16
msgid "Search Product"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:30
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:30
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:67
msgid "Select Product"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:35
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:35
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:14
#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:14
msgid "all time"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:114
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:114
msgid "Purchase Before"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:124
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:124
msgid "Purchase Quantity Should Be"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-against-product.php:132
#: App/Views/Admin/Conditions/PurchaseHistory/previous-order-quantities-against-product.php:132
msgid "Purchase Quantity"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:93
msgid "purchase before"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:103
msgid "Order quantity should be"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/previous-order.php:110
msgid "Purchased quantity"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:93
msgid "Purchase history"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:103
msgid "Purchased amount should be"
msgstr ""

#: App/Views/Admin/Conditions/PurchaseHistory/spent.php:111
msgid "Amount"
msgstr ""

#: App/Views/Admin/Conditions/Shipping/country.php:15
msgid "Countries should be"
msgstr ""

#: App/Views/Admin/Conditions/Shipping/country.php:23
#: App/Views/Admin/Conditions/Shipping/state.php:25
msgid "Search Country"
msgstr ""

#: App/Views/Admin/Conditions/Shipping/country.php:37
#: App/Views/Admin/Conditions/Shipping/state.php:39
msgid "Select Country"
msgstr ""

#: App/Views/Admin/Conditions/Shipping/state.php:17
msgid "States should be"
msgstr ""

#: App/Views/Admin/Conditions/Shipping/state.php:47
msgid "Search State"
msgstr ""

#: App/Views/Admin/Conditions/Shipping/state.php:62
msgid "Select State"
msgstr ""

#: App/Views/Admin/Conditions/Shipping/zip-code.php:14
msgid "zipcode should be"
msgstr ""

#: App/Views/Admin/Conditions/Shipping/zip-code.php:19
msgid "Enter Zipcode "
msgstr ""

#: App/Views/Admin/Conditions/Shipping/zip-code.php:21
msgid "Example: 94027, 90210"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-x-range.php:14
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:22
msgid "Min Quantity"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-x-range.php:17
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:25
msgid "Minimum Quantity"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-x-range.php:23
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:31
msgid "Max Quantity"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-x-range.php:34
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:43
msgid "Maximum Quantity"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-x-range.php:40
#: App/Views/Admin/Discounts/buy-x-get-x-range.php:43
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:135
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:138
msgid "Free Quantity"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-x-range.php:51
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:166
msgid "Percentage discount"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-x-range.php:52
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:167
msgid "Fixed discount"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-x-range.php:54
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:169
msgid "Discount type "
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-x-range.php:62
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:179
msgid "Discount value "
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-x-range.php:62
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:179
msgid "Discount percentage "
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-x-range.php:79
#: App/Views/Admin/Discounts/buy-x-get-y-range.php:197
#: App/Views/Admin/Discounts/set-range.php:82
msgid "Recursive?"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-x.php:34
#: App/Views/Admin/Discounts/buy-x-get-y.php:108
#: App/Views/Admin/Discounts/set.php:86
msgid "Add Range"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y-range.php:18
msgid "Buy Quantity"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y-range.php:47
msgid "Get Quantity"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y-range.php:146
msgid "Following products might not get discount as you have chosen from different language"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y.php:41
msgid "Select Types"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y.php:44
msgid "Buy X Get Y - Products "
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y.php:47
msgid "Buy X Get Y - Categories"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y.php:50
msgid "Buy X Get Y - All"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y.php:55
msgid "Get Y Discount Type "
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y.php:61
#: App/Views/Admin/Discounts/set.php:49
msgid "Filters set above"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y.php:64
msgid "Individual Product"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y.php:67
#: App/Views/Admin/Discounts/set.php:64
msgid "All variants in each product together"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y.php:70
msgid "Buy X Count Based on "
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y.php:82
msgid "Auto add"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y.php:83
msgid "Cheapest"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y.php:84
msgid "Highest"
msgstr ""

#: App/Views/Admin/Discounts/buy-x-get-y.php:86
msgid "Mode of Apply"
msgstr ""

#: App/Views/Admin/Discounts/set-range.php:22
msgid "Quantity "
msgstr ""

#: App/Views/Admin/Discounts/set-range.php:26
msgid "for "
msgstr ""

#: App/Views/Admin/Discounts/set-range.php:39
msgid "Discount Value "
msgstr ""

#: App/Views/Admin/Discounts/set-range.php:45
msgid "Fixed price for set / bundle"
msgstr ""

#: App/Views/Admin/Discounts/set-range.php:48
msgid "Percentage discount per item"
msgstr ""

#: App/Views/Admin/Discounts/set-range.php:51
msgid "Fixed discount per item"
msgstr ""

#: App/Views/Admin/Discounts/set-range.php:53
msgid "Discount Type"
msgstr ""

#: App/Views/Admin/Discounts/set-range.php:59
msgid "label"
msgstr ""

#: App/Views/Admin/Discounts/set-range.php:62
msgid "Title column For Bulk Table"
msgstr ""

#: App/Views/Admin/Discounts/set.php:18
msgid "Count Quantities by:"
msgstr ""

#: App/Views/Admin/Discounts/set.php:18
msgid ""
"Filter set above : \n"
"This will count the quantities of products set in the “Filter” section.\n"
"Example: If you selected a few categories there, it will count the quantities of products in those categories added in cart. If you selected a few products in the filters section, then it will count the quantities together.\n"
"\n"
"Example: Let’s say, you wanted to offer a Bulk Quantity discount for Category A and chosen Category A in the filters.\n"
"\n"
"So when a customer adds 1 quantity each of X, Y and Z from Category A, then the count here is 3. \n"
"\n"
"Individual Product :\n"
"\n"
"This counts the total quantity of each product / line item separately.\n"
"Example : If a customer wanted to buy 2 quantities of Product A,  3 quantities of Product B, then count will be maintained at the product level. \n"
"2 - count of Product A\n"
"3 - Count of Product B\n"
"\n"
"In case of variable products, the count will be based on each variant because WooCommerce considers a variant as a product itself.  \n"
"\n"
"All variants in each product together :\n"
"Useful when applying discounts based on variable products and you want the quantity to be counted based on the parent product.\n"
"Example: \n"
"Say, you have Product A - Small, Medium, Large.\n"
"If a customer buys  2 of Product A - Small,  4 of Product A - Medium,  6 of Product A - Large, then the count will be: 6+4+2 = 12\n"
""
msgstr ""

#: App/Views/Admin/Discounts/set.php:42
msgid ""
"This will count the quantities of products set in the “Filter” section.\n"
"Example: If you selected a few categories there, it will count the quantities of products in those categories added in cart. If you selected a few products in the filters section, then it will count the quantities together.\n"
"\n"
"Example: Let’s say, you wanted to offer a Bulk Quantity discount for Category A and chosen Category A in the filters.\n"
"\n"
"So when a customer adds 1 quantity each of X, Y and Z from Category A, then the count here is 3."
msgstr ""

#: App/Views/Admin/Discounts/set.php:50
msgid ""
"This counts the total quantity of each product / line item separately.\n"
"Example : If a customer wanted to buy 2 quantities of Product A,  3 quantities of Product B, then count will be maintained at the product level. \n"
"2 - count of Product A\n"
"3 - Count of Product B\n"
"\n"
"In case of variable products, the count will be based on each variant because WooCommerce considers a variant as a product itself.  \n"
""
msgstr ""

#: App/Views/Admin/Discounts/set.php:58
msgid "Individual product"
msgstr ""

#: App/Views/Admin/Discounts/set.php:59
msgid ""
"Useful when applying discounts based on variable products and you want the quantity to be counted based on the parent product.\n"
"Example: \n"
"Say, you have Product A - Small, Medium, Large.\n"
"If a customer buys  2 of Product A - Small,  4 of Product A - Medium,  6 of Product A - Large, then the count will be: 6+4+2 = 12"
msgstr ""

#: App/Views/Admin/Discounts/set.php:95
msgid "Show discount in cart as coupon instead of changing the product price ?"
msgstr ""

#: App/Views/Admin/Discounts/set.php:102
msgid "Discount Label"
msgstr ""

#: App/Views/Admin/Filters/category.php:19
msgid "Select Categories"
msgstr ""

#: App/Views/Admin/Filters/common_edit.php:97
#: App/Views/Admin/Filters/common_edit.php:153
msgid "Select "
msgstr ""

#: App/Views/Admin/Filters/taxonomies.php:11
msgid "In list"
msgstr ""

#: App/Views/Admin/Filters/taxonomies.php:12
msgid "Not in list"
msgstr ""

#: App/Views/Admin/Filters/taxonomies.php:22
msgid "Select values"
msgstr ""

#: App/Views/Admin/Settings/cart.php:8
msgid "Free shipping method title"
msgstr ""

#: App/Views/Admin/Settings/cart.php:9
msgid "This will be used as a method title in the cart / checkout pages"
msgstr ""

#: App/Views/Admin/Settings/cart.php:19
msgid "Hide other shipping options when free shipping available"
msgstr ""

#: App/Views/Admin/Settings/cart.php:20
msgid " Use this if you would like to hide other shipping options when a free shipping discount available"
msgstr ""

#: App/Views/Admin/Settings/general.php:8
msgid "License Key"
msgstr ""

#: App/Views/Admin/Settings/general.php:9
msgid "Activate your license for Discount Rules PRO. Retrieve your license key from <a target=\"_blank\" href=\"https://my.flycart.org/my-account\">your account</a> and enter it here. If you haven't purchased a license yet, visit <a target=\"_blank\" href=\"https://www.flycart.org/products/wordpress/woocommerce-discount-rules\">flycart.org</a> to buy one today."
msgstr ""

#: App/Views/Admin/Settings/general.php:9
msgid "Read Docs"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:8
msgid "Enable Buy X Get Y based Cross-sell Offers"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:9
msgid "NOTE: This setting applies only for Buy X Get Y - Product rule type"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:26
msgid "Number of Products"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:27
msgid "Number of items to show"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:35
msgid "Columns"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:36
msgid "Number of columns"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:44
msgid "Sorting Order"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:45
msgid "Sorting order"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:50
msgid "Random ordering"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:51
msgid "Menu based ordering"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:52
msgid "Price based ordering"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:58
msgid "Ordering"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:59
msgid "Ascending or descending order of items"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:64
msgid "Desc"
msgstr ""

#: App/Views/Admin/Settings/promotion.php:65
msgid "Asc"
msgstr ""

#: App/Views/Templates/buy-x-get-y-select-auto-add-variant.php:12
msgid "Change Variant"
msgstr ""

#: App/Views/Templates/cross-sells.php:20
msgid "You may be interested in"
msgstr ""

#: woo-discount-rules-pro.php:84
msgid "Woo Discount Rules 2.0"
msgstr ""

#: woo-discount-rules-pro.php:87
msgid "Since 2.0, you need both the core and pro packages installed and activated."
msgstr ""

#: woo-discount-rules-pro.php:90
msgid "Why we made this change?"
msgstr ""

#: woo-discount-rules-pro.php:93
msgid "This arrangement is to avoid the confusion in the installation and upgrade process. Many users first install the core free version. Then purchase the PRO version and try to install it over the free version. Since both free and pro packages have same names, wordpress asks them to uninstall free and then install pro. As you can see, this is quite confusing for the end users."
msgstr ""

#: woo-discount-rules-pro.php:96
msgid "As a result, starting from 2.0, we now have two packs: 1. Core 2. PRO."
msgstr ""

#: woo-discount-rules-pro.php:99
msgid "What do I need to do?"
msgstr ""

#: woo-discount-rules-pro.php:102
msgid " - Just install both and activate both Core and Pro packs."
msgstr ""

#: woo-discount-rules-pro.php:105
msgid "Install core version"
msgstr ""

#: woo-discount-rules-pro.php:108
msgid "Simple and straight-forward (no uninstalls and re-installs)."
msgstr ""

#: woo-discount-rules-pro.php:125
#: woo-discount-rules-pro.php:126
msgid "Discount Rules"
msgstr ""
