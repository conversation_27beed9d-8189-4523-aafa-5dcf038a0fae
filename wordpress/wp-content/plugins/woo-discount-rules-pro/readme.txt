=== Discount Rules for WooCommerce ===
Contributors: flycart
Donate link: https://flycart.org/
Tags: woocommerce, coupons, discounts, dynamic pricing, bulk discount
Requires at least: 4.6.1
Tested up to: 6.8
Stable tag: 2.6.10
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-3.0.html

The discount plugin for WooCommerce helps you create bulk discount, quantity discount, storewide sale, dynamic pricing discount offers easily.

== Description ==

[Discount Rules for WooCommerce](https://www.flycart.org/products/wordpress/woocommerce-discount-rules) is a dynamic pricing and discounts plugin for WooCommerce that helps you create any type of bulk discounts, quantity discounts, product specific discounts, dynamic pricing, advanced discounts based on conditions, percentage discounts, tiered pricing discounts and more.

With the [PRO version](https://www.flycart.org/products/wordpress/woocommerce-discount-rules), you can create advanced discount offers such as **buy one and get one free (BOGO) deals, auto-apply coupons, free shipping discounts, bundle offers,  discounts based on products, categories, attributes, tags, SKUs, user roles, cart items, purchase history** and much more.

[youtube https://www.youtube.com/watch?v=Ni0JjeyHyzM]

The plugin offers a number of features that make it easy for customers to discover discounts and offers in your store:

* Show a bulk discount table on your product pages. This helps customers buy more in order to save more.
* Show the discounted price on product, cart & checkout pages with the original price crossed-out (with a line-through)
* Display "You saved" message on the cart & checkout pages
* Show a discount bar with your offers on the product pages
* Display a Sale badge on your shop & product pages

[View Demo](https://demo.flycart.net/woo-discount-rules) | [Documentation with examples](https://docs.flycart.org/en/collections/806883-discount-rules-for-woocommerce) | [Ask Questions](https://www.flycart.org/support) | [PRO version](https://www.flycart.org/products/wordpress/woocommerce-discount-rules) | [Examples](https://www.flycart.org/woocommerce-discount-rules-examples)

== DISCOUNT TYPES AND FEATURES WITH EXAMPLE SCENARIOS ==

= Free version features =

* Create [percentage discounts](https://www.flycart.org/blog/wordpress/how-to-create-percentage-discounts-in-woocommerce).
* Offer a [storewide sale](https://www.flycart.org/blog/wordpress/how-to-add-a-storewide-discount-in-woocommerce) Example: A storewide 10% discount on all products
* Create [bulk discounts](https://docs.flycart.org/en/articles/3807208-bulk-discounts-or-tiered-pricing). Example: Use quantity breaks to offer higher discounts on bulk purchases.
* Order total based discounts (Example: Spend more than $1000, get 10% discount)
* Set product specific percentage discounts (Example: Product A gets 10%, Product B gets 20%)
* Cart based discounts using the line items (Example: Purchase 4 different products or variants and get 10% discount)
* Exclude selected products from discount rules (product pricing discounts)
* Run special offers / sale for specific periods - Example: 10% discount only on BlackFriday
* Show bulk discount table on product pages

[Get the PRO version](https://www.flycart.org/products/wordpress/woocommerce-discount-rules) to create advanced discounts and grow sales

= PRO features =
All features of the free version, plus:

* Percentage discounts with advanced discount conditions.
* Fixed product discounts. Example: Get $9 discount for purchasing more than 6 items
* [Category discounts](https://www.flycart.org/blog/wordpress/how-to-create-woocommmerce-category-discounts) - Get 25 % off on all items under Summer Collection
* [Buy One Get One free offers](https://www.flycart.org/blog/woocommerce/how-to-create-buy-one-get-one-bogo-offers-in-woocommerce) - Buy a cap and get another cap for free
* [Buy 2 and get 1 free offers](https://docs.flycart.org/en/articles/3953967-buy-x-and-get-y-product-for-free-or-at-50-discount) - Buy 2 get 1 cheapest product free or Buy 2 and get 1 at 50% discount
* [Buy one get one half off](https://www.flycart.org/blog/woocommerce/how-to-create-buy-one-get-one-bogo-offers-in-woocommerce) - Buy 2 get 1 at 50% off or buy one get the second product free or at 50% discount
* [BOGO deals based on categories](https://docs.flycart.org/en/articles/3946511-buy-any-items-from-category-a-and-get-20-discount-on-category-b). Examples: Buy any product from Category A and get a product free from Category B. Buy Product A and Product B from Category Electronics and get a free product from category Accessories. Buy any product from Category Mobile and choose a free product from Category Hard Cases
* [Volume discounts & tiered quantity discounts with conditions](https://www.flycart.org/blog/woocommerce/how-to-create-bulk-discounts-in-woocommerce) - Buy 6, get 10%, Buy 12, get 15%
* [User role based discounts](https://docs.flycart.org/en/articles/4203313-discount-based-on-user-role) - Discounts for wholesale customers
* [First order discount](https://docs.flycart.org/en/articles/4206683-discount-based-on-first-order)
* [Product discounts with variant specific offers](https://docs.flycart.org/en/articles/4217898-discounts-on-specific-product-simple-and-variable-products-from-version-2-0) - Buy Product A with Variant X and get 10% discount
* [Bundle discount](https://docs.flycart.org/en/articles/3809899-bundle-set-discount) -  Buy 3 for $10, 6 for $20
* Conditional discounts - Buy Product A and B and get discount on Product C
* Offer one or more free products. Multiple products could be offered free using a rule
* [Apply discounts for multiple items using one coupon code](https://docs.flycart.org/en/articles/4268595-activate-discount-rule-using-a-coupon-code-in-woocommerce)
* Discount for product variants
* Attributes based discounts - Buy green color T-shirts and get 10% discount
* Discount for customers with specific domains - 10% discount for all emails ending with @example.com
* Customer specific discounts - 10% discount for selected customers
* [Free shipping discount](https://docs.flycart.org/en/articles/3807036-free-shipping)
* [Shipping / Delivery location based discount](https://docs.flycart.org/en/articles/4214869-customer-shipping-address-based-discount-2-0) Example: If shipping destination is California, get 15% discount
* [Purchase history based discounts](https://docs.flycart.org/en/articles/4774928-discount-based-on-purchase-history)  Example: Customers who spent at least $100 in previous purchases get 10% discount
* Discount based on the number of orders placed earlier. Example: 10% discount for customers with 5 or more orders
* Discount based on previously purchased products. Example: Customers who previously purchased Shoes get 10% discount
* Discounts based on sum of item quantities in cart - Order more than 10 items from any category and get 15 % discount
* Discount for the cheapest item in the cart - Add 3 products to the cart and get the Cheapest Item free
* Option to offer free products only from certain category
* Exclude products on SALE from discount rules
* SALE Badge for discounted products
* Highlight savings on the cart & checkout with "You saved" label
* Use a discount banner to highlight offers on product pages
* Option to show or hide the discounted price
* Priority support

== Discount Examples using PRO Features ==

== Category Discount ==
The category discount feature allows you to offer discounts on all products in one or more categories. Simply choose one or more categories, set your discount offers, and the plugin will apply them automatically to every product in those categories. Here are some examples:

* Buy T-shirts from Apparel category and save 10%
* Get 30% off on Category A & Category B
* Purchase any 6 items from Category A and get a 25% discount on total cart value.
* Purchase 3 products from Category A or B or C or from all of them and get 20% discount valid from dd/mm/yyyy to dd/mm/yyyy

== Bulk Discount / Quantity Discount / Tiered Pricing Discounts ==
Bulk Discount, also known as quantity discounts, tiered pricing discounts or volume discounts, allows you to reward customers for buying in bulk. As customers purchase more, they receive better pricing, encouraging bulk purchases. Here are some bulk discount examples:

* [Buy 5 to 10 quantities, get 5% discount, 11-20 quantities, get 10% discount, 21-30, get 20% discount](https://docs.flycart.org/en/articles/3807208-bulk-discounts-or-tiered-pricing)
* Buy 1 to 5 quantities, get $2 discount, Buy 6 or more, get $3 discount

== Storewide Sale Discount ==
A Storewide Sale discount allows you to apply discounts on all products in your store, offering a simple yet powerful way to drive sales. Whether you're running a seasonal sale, clearing out inventory, or rewarding loyal customers, a storewide discount makes it easy for shoppers to save on everything in your store.

* 20% discount on all products in the store on Black Friday or Christmas
* Auto apply coupon of 10% for all products
* 10% discount on all products for specific user role like Wholesale customers

== Buy One Get One Free Discount Offers ==

BOGO is a popular discount strategy that allows you to create offers like buy one get one free, buy 2 get 1 free, buy one get one at half price, buy x and get y product free and more. Here are some examples:

* [Buy one and get one free of the same product](https://docs.flycart.org/en/articles/3810071-buy-one-get-one-free-buy-x-get-x)
* [Buy 2 get one cheapest free](https://docs.flycart.org/en/articles/3810570-buy-x-get-y)
* Buy X get Y product free  Eg: Buy 3 product and get a pendrive free (or with 50% discount)
* Buy Any 2 items from Category X, and get a Product free from Category B
* Buy X products and get X number of products free
* Buy 3 products from any category, get 1 free. Buy 6 products, get 2 products free....
* Buy 12 products (any or category specific), get X number of cheapest among them free
* Free products can be a few selected products, cheapest products in the entire store or from selected categories.
* Products can be offered either free (100% discount) or at a percentage discount (50%)

== Spending based discounts  ==
Boost sales by offering discounts based on the customer spend. Here are some examples:

* [Spend $500 or more and get 15% discount, spend $1000, get 20% discount](https://docs.flycart.org/en/articles/3894861-subtotal-based-tiered-discounts)
* Add two to six products in cart and get flat $30 discount.
* 10% discount for all retailers (User Role specific discount)
* All orders above $500 will get 15% discount
* Free shipping for orders above $100

== Bundle Discount ==
Encourage customers to buy multiple items together. For example, you can create deals like "Buy 3 for $10" or "Buy 6 for $20," providing clear savings for bulk purchases.

* Create offers like Buy 3 items for $10. 4th item will be charged full price.
* Buy more, save more offers. Example: Buy 3 for $10, 6 for $20

== Product Specific Discounts ==

* Shoes get 20% discount, T-Shirts get 5% discount
* Special Edition Shoe (A specific product) gets 15% discount for 10 days
* Buy 10 or more Mugs and get 5% off
* Buy 3 for $10, Buy 6 for $20

== Fixed Product Pricing discounts ==

* $5 off on all products in Category A
* Set a fixed price per unit for bulk purchases (Product A cost is $20. Buy 5 to 10 quantity for $15 per unit, 11 and above for $10 each)

== User Role based discounts  ==
Useful when you have different types of customers in your store. For example, wholesale customers, retail customers

* Bulk discount based on user role for specific product or product categories. Example: Members of Wholesale customers group gets 40% discount, while Retail customers get 5% discount

== Discount for Product Variants ==

* Get 50% off on T-shirts Small and Large Sizes only
* Buy a Small Size Tee and get a small Size shorts free

== Conditional Discount Offers ==

* [Buy Product A at full price and get 20% discount in Product B](https://docs.flycart.org/en/articles/3953967-buy-x-and-get-y-product-for-free-or-at-50-discount-discount-rules-2-0)
* Buy 2 quantities of Product A, 2 quantities of Product and get 30% discount in Product C
* Buy 3 quantities of Product A and get flat $10 off in product B
* Buy a Mobile and get 20% off on headphone

== Attribute specific discount offers ==

Useful when you want to offer discount when a product has a specific attribute. Example: 10% discount for purchase of T-Shirt with Small sizes.

== Shipping Address Based Discounts ==

* Flat 25% discount for customers from New York
* Customers from California get 10% discount while those from Texas get 5%

== Purchase history based discount ==

* [Discount based on the total amount spent by the customer in previous orders](https://docs.flycart.org/en/articles/4774928-discount-based-on-purchase-history). Example: Customers who spent $1000 get 10% on future orders.
* Discount based on the total number of orders placed by the customer - Customers who ordered 50 products get 15% on all future purchases
* Based on product purchase history (Example: Provide 10% discount to all customers who purchased Shoes earlier)
* Purchase history for a specific period (10% discount for those who spent $500 in the last 3 months)

= Discount Conditions =

The following conditions help you tailor your discount offers depending on certain criteria.

* Products
* Categories
* Attributes
* SKU
* Tags
* Cart Line Items & Properties
* Customers
* User Roles
* Shipping locations - ZipCode, City, State/Region, Country
* Subtotal
* Coupons
* Date & Time
* Purchase History - Last Order, First Order, Orders with X Items, Number of Orders

= Need help? Reach out to us =

Whether you want to double-check your setup or need help with a unique discount promo, we’re here to assist you. Feel free to reach out via the Live Chat on our website or through our [support request form](https://www.flycart.org/support). We’re happy to guide you through the setup and ensure everything is running smoothly. Don’t worry—we’re here to help!

Quick Links:

* [PRO Version](https://www.flycart.org/products/wordpress/woocommerce-discount-rules)
* [Documentation](https://docs.flycart.org/en/collections/806883-discount-rules-for-woocommerce)
* [Helpdesk & Support](https://www.flycart.org/support)

== External services ==
Flycart CDN & documentation: The CDN is used to fetch static assets including images and contentand hosted at static.flycart.net, while the documentation for the plugin is hosted at docs.flycart.org and linked at various sections in the plugin to help merchants. [Terms](https://www.flycart.org/terms-conditions), [privacy policy](https://www.flycart.org/privacy-policy)
Google Charts: The library is used to display discount reports in a chart format. The service is provided by Google. [Terms](https://developers.google.com/chart/terms), [privacy policy](https://developers.google.com/chart/interactive/docs/security_privacy)

== Installation ==

Just use the WordPress installer or upload to the /wp-content/plugins folder. Then Activate the Discount Rules plugin.
More information could be found in the documentation

= Minimum Requirements =

* WordPress 6.0 or greater
* WooCommerce 6.0.0 or greater
* PHP version 7.0 or greater
* MySQL version 5.0 or greater

== Frequently asked questions ==

= Is it possible to create multiple discount offers ? =

Yes. It is possible to create multiple discount offers. You can let them apply based on conditions

= How to create a bulk discount ? =
After installing the Discount Rules plugin for WooCommerce, navigate to your WordPress dashboard -> WooCommerce -> Discount Rules -> Create a new rule and choose the discount type as "Bulk Discount". Add quantity tiers and discount for each tier before going live

= How to create quantity breaks? =
Navigate to your WordPress dashboard -> WooCommerce -> Discount Rules -> Create a new rule and choose the discount type as "Bulk Discount". Click on the Add range button to create quantity ranges and set a discount for each range. Once done, publish the discount

= How to create a volume discount? =
Volume discounts are similar to creating the bulk discounts. Navigate to your WordPress dashboard -> WooCommerce -> Discount Rules -> Create a new rule and choose the discount type as "Bulk Discount". Click on the Add range button to create quantity ranges and set a discount for each range. Once done, publish the discount.

= How to create a percentage discount? =
Creating a percentage discount is quite easy. Navigate to your WordPress dashboard -> WooCommerce -> Discount Rules -> Create a new rule and choose the discount type as "Product Adjustment". In the Filters section, choose either All Products or select the products that you would like to discount. In the discount section, enter the percentage value. Example: A 10% discount. Once done, save and publish

= How to create a store-wide sale? =
Navigate to your WordPress dashboard -> WooCommerce -> Discount Rules -> Create a new rule and choose the discount type as "Product adjustment". In the Filters section, choose All Products. In the discount section, choose the percentage option and enter a value. Example: 10%. Save and publish the rule.


= Can I display discounts on the product page ? =

Yes. Use a sale badge, a discount banner or a bulk discount table to showcase the offers on the product page.

= Is it possible to exclude products on SALE? =

Yes. You can exclude the products on sale from the discount rules. There is a filter available

= Can I offer a discount based on the purchase history of the customer? =

Yes. You can offer a discount based on the purchase history, total amount spent by the customer in his previous orders or based on the total number of orders placed by a customer.

= Is it possible to provide discount for wholesale customers? =

Yes. The plugin has a user role specific discount rule. You can create a discount for specific user roles like Wholesale customers.

= Will the discount be showed in the invoice ? =

Yes. The discount will be displayed separately in the invoice when using cart based discount rules.

= Will the discount be applied to Product Variants ? =

Yes. The discount will be applied to Product Variants as well. If you have two variants for an iPhone such as 64GB and 128GB, the discount will be applicable for both the variants.

= Can I set a global discount for all products ? =

Yes. After installing the Discount Rules plugin, create a new rule setting up either a percentage or fixed discount for all products in your store.

= If I have one or more rules for the same product, which will be applied ? =

In that case, the rule with the higher priority will be applied.


== Screenshots ==

1. Price rules and Cart rules
2. Creating a price rule
3. Example Promotion Offers created using Discount rules
4. Discounted price is applied in the Cart
5. Creating a Buy X get Y rule
6. Free product auto added in cart

== Credits ==

* Dutch Translation - [@mvdburg1971](https://profiles.wordpress.org/mvdburg1971) - Michael van der Burg

== Changelog ==
= 2.6.10 - 24/06/25 =
* Fix: Coupon case-sensitive.
* Added: Compatibility for WordPress 6.8
* Added: Compatibility for WooCommerce 9.9

= 2.6.9 - 11/04/25 =
* Updated readme.txt file.
* Fix: Plugin check listed issue.

= 2.6.8 - 24/03/25 =
* Added: Recommendation tab [Core].
* Fix: Same price strikeout [Core].
* Fix: Validation for brands [PRO].

= 2.6.7 - 19/02/25 =
* Added: Event: advanced_woo_discount_rules_free_product_price [PRO].
* Added: jQuery event trigger wdr_dynamic_price_update [Core].
* Added: Event: wlr_is_purchasable_need_to_check [Core].
* Added: Event: advanced_woo_discount_rules_cart_total_saved_text [Core].
* Added: Event: advanced_woo_discount_rules_is_allow_tax_calculation_for_fee [Core].
* Added: Event: advanced_woo_discount_rules_on_sale_list_filters [Core].
* Improved: Coupon code compares in CartCoupon Condition [PRO].

= 2.6.6 -10/09/24 =
* Fix - Minor XSS issue in the review request notice [Core].

= 2.6.5 - 14/08/24 =
* Improved: Enhanced product search functionality.
* Improved: label changes.

= 2.6.4 - 11/06/24 =
* Add: Compatibility for WordPress 6.5
* Add: Compatibility for WooCommerce 8.9
* Add: Event: advanced_woo_discount_rules_check_purchase_first_order_status [PRO].
* Improved: Strikeout price suffix.
* Improved: Collection add-on support [PRO].
* Improved: Taxonomies filter processing [PRO].

= 2.6.3 - 20/02/24 =
* Add: Compatibility for WordPress 6.4
* Add: Compatibility for WooCommerce 8.6
* Improved: Plugin name changed
* Fix: Warnings when enabled "Suppress third party discount plugins" option in settings

= 2.6.2 - 27/09/23 =
* Add: Compatibility for WooCommerce 8.1

= 2.6.1 - 12/07/23 =
* Improvement: Pagination improvements [Core].
* Improvement: Text changes [Core].
* Improvement: Event: advanced_woo_discount_rules_matched_set_discount_range [Pro].
* Add: Support collection addon(v1.2.0) on BXGY [Pro].
* Fix: Duplicate coupon message while using URL coupon [Core].
* Fix: Tax not included on dynamic strikeout [Core].
* Fix: Tax not included on discount table for variable products [Core and Pro].
* Fix: BXGY variants not working on few cases [Pro].

= 2.6.0 - 08/05/23 =
* Add: Pagination on backend rule listing [Core].
* Add: Support WooCommerce High-Performance order storage feature [Core and Pro].
* Add: Tab for display Addons [Core].
* Add: Option to exclude out of stock product on on-sale page [Core].
* Improvement: Query optimization while load coupon in backend [Pro].
* Improvement: Show message in plugin page when a major release is available [Core].
* Improvement: Allow span, div and p tag on HTML accepted fields [Core].
* Improvement: Changed TEXT to LONGTEXT for the columns filters, conditions [Core].
* Improvement: Display single value on discount table when start and end range is same [Core].
* Improvement: Licence URL update [Pro].
* Improvement: Load individual product count through the event advanced_woo_discount_rules_include_cart_item_to_count_quantity [Core].
* Improvement: Support {{cart_subtotal}} shortcode on promotion message. [Core].
* Improvement: Event: advanced_woo_discount_rules_get_product_discount_price [Core].
* Improvement: Event: advanced_woo_discount_rules_get_product_discount_details [Core].
* Improvement: Event: advanced_woo_discount_rules_get_product_discount_percentage [Core].
* Improvement: Event: advanced_woo_discount_rules_get_product_save_amount [Core].
* Improvement: Event: advanced_woo_discount_rules_get_cart_item_discount_price [Core].
* Improvement: Event: advanced_woo_discount_rules_get_cart_item_discount_details [Core].
* Improvement: Event: advanced_woo_discount_rules_get_cart_item_saved_amount [Core].
* Improvement: Event: advanced_woo_discount_rules_get_order_item_discount_price [Core].
* Improvement: Event: advanced_woo_discount_rules_get_order_item_discount_details [Core].
* Improvement: Event: advanced_woo_discount_rules_get_order_item_saved_amount [Core].
* Improvement: Event: advanced_woo_discount_rules_get_order_discount_details [Core].
* Improvement: Event: advanced_woo_discount_rules_get_order_saved_amount [Core].
* Improvement: Event: advanced_woo_discount_rules_cart_subtotal_promotion_message [Core].
* Fix: Warning when regular price in not entered on product [Core].
* Fix: Loading range on backend - disappear when remove the first one [Core and Pro].
* Fix: Wrong calculation in cart discount while enable apply all matched rule with Apply discount sequentially option [Core].
* Fix: Showing wrong strikeout on product page [Core].
* Fix: Adding discount info meta field even if no discount applied [Core].

= 2.5.4 - 30/01/23 =
* Fix: Discount data is not stored for analytics when change language using WPML [Core].
* Fix: Discount value is incorrect on coupon report for fixed discount type [Core].
* Fix: Dynamic strikeout not working for variable product [Core].

= 2.5.3 - 16/01/23 =
* Improvement: Show used coupon report [Core]
* Improvement: Dynamic strikeout script update to check the target first with in form [Core].
* Improvement: Updated JQuery UI version [Core].
* Fix: Validation fix on creating rules [Pro].
* Fix: Warning when on rule conditions due to translation [Core].
* Fix: The cheapest product does not receive a discount when variant together option is enabled [Pro].
* Fix: Error on variation is not an array [Pro].
* Fix: Condition doesn't matches when have Match any option with user role condition [Pro].
* Fix: On-sale page shows incorrect data while have attribute in filters [Pro].

= 2.5.2 - 09/11/22 =
* Improvement: Event: advanced_woo_discount_rules_exclude_coupon_while_remove_third_party_coupon [Core].
* Improvement: Updated code on check free shipping [Core].
* Add: Support for WordPress 6.1 [Core and Pro].
* Fix: SKU filter displays wrong value, when having space in SKU [Pro].

= 2.5.0 - 01/11/22 =
* Improvement: Added applied discount info in order and order item meta _wdr_discounts [Core and Pro].
* Improvement: Loading issue on shop page while having variable price strikeout improvement [Core].
* Improvement: Set 3 for 10 fixed .1 difference on subtotal [Pro].
* Improvement: Doing strikeout on cart while using third-party shortcode [Core].
* Improvement: Show free shipping rule reports since v2.5.0 [Pro].
* Improvement: Added order_item_id and other_discount columns in wdr_order_item_discounts table [Core].
* Add: Show total order count and sales on report section [Core and Pro].
* Fix: Disable coupon option doesn't working with free shipping [Pro].
* Fix: Applied message is not displaying in cart for free shipping [Pro].
* Fix: Limit option is not working with Free shipping [Pro].
* Fix: Warning on PHP 8 [Core and Pro].
* Fix: Fatal error on load order item meta [Core].

= 2.4.5 - 27/09/22 =
* Improvement: Additional param on the event advanced_woo_discount_rules_strikeout_price_html [Core].
* Improvement: Backend field validation [Core and Pro].
* Improvement: Condition and filter matches on any operation [Core and Pro].
* Fix: Applying discount more than discount quantity on set and BXGY [Pro]
* Fix: Quantity issue on set discount while manual request [Pro].

= 2.4.4 - 09/08/22 =
* Improvement: Event: advanced_woo_discount_rules_is_valid_filter_type
* Improvement: Event: advanced_woo_discount_rules_after_delete_rule
* Improvement: Event: advanced_woo_discount_rules_after_delete_rules
* Improvement: Event: advanced_woo_discount_rules_page_tabs
* Improvement: Event: advanced_woo_discount_rules_process_custom_filter
* Improvement: Event: advanced_woo_discount_rules_load_custom_filter_data
* Improvement: Event: advanced_woo_discount_rules_update_additional_data_before_save_rule
* Improvement: Event: advanced_woo_discount_rules_after_save_rule
* Improvement: Event: advanced_woo_discount_rules_do_strikeout_for_out_of_stock_variants
* Fix: Onsale page query improvement for on-sale filter.
* Fix: Redirecting to list page on create rule instead of edit page.
* Fix: BXGY cheapest variants together strikeout issue fix
* Fix: Wrong price on subtotal promotion message while set including tax.

= 2.4.3 - 28/06/22 =
* Improvement: Event: advanced_woo_discount_rules_is_valid_filter_type
* Improvement: Event: advanced_woo_discount_rules_after_delete_rule
* Improvement: Event: advanced_woo_discount_rules_after_delete_rules
* Improvement: Event: advanced_woo_discount_rules_page_tabs
* Improvement: Event: advanced_woo_discount_rules_process_custom_filter
* Improvement: Event: advanced_woo_discount_rules_load_custom_filter_data
* Improvement: Event: advanced_woo_discount_rules_update_additional_data_before_save_rule
* Improvement: Event: advanced_woo_discount_rules_after_save_rule
* Fix: Onsale page query improvement for on-sale filter.
* Fix: Redirecting to list page on create rule instead of edit page.
* Fix: Sale page doesn't filters based on selected rules.
* Improvement: Event: advanced_woo_discount_rules_get_price_of_cart_item_on_find_cheapest_item

= 2.4.2 - 21/06/22 =
* Feature: Option to schedule sale page product rebuild on daily once.
* Improvement: Event: advanced_woo_discount_rules_variation_title_modify_count
* Improvement: Event: advanced_woo_discount_rules_refresh_shipping_options_on_order_review
* Fix: Cart coupon Condition not working when cart is empty.
* Fix: Fatal error due to Invalid Product ID on save rule.
* Fix: Warning on ajax strikeout.
* Fix: Strikeout not displayed for BXGY cheapest individual product on product page and cross sell block.
* Fix: BXGY discount applies before matches.
* Fix: esc_attr() applied to prevent XSS issues.

= 2.4.1 - 17/05/22 =
* Improvement: Added template override path for Bulk table.
* Improvement: Removed backslash while using single quote on promotion message.
* Improvement: Event: advanced_woo_discount_rules_allowed_html_elements_and_attributes.
* Improvement: Load rule id on discount table info for BXGY and Set discount.
* Improvement: V1 to v2 migration limit.
* Fix: Table compare issue.
* Fix: Division by zero.
* Fix: Warning on get cart.

= 2.4.0 - 06/04/22 =
* Deprecated: V1 layout.
* Fix: Calculating discount for out of stock product variant.
* Fix: Custom taxonomy query while process on-sale page.
* Fix: Multiple messages on having BXGY cheapest rules.
* Improvement - Event advanced_woo_discount_rules_cart_strikeout_quantity_html.

= 2.3.13 - 09/02/22 =
* Feature - Apply coupon through URL option.
* Improvement - Compatible option for Multi-Currency by WPML.
* Improvement - Compatible for WooCommerce Multi-Currency by TIV.NET.
* Improvement - Compatible for WooCommerce Price Based on Country by Oscar Gare.
* Improvement - Quantity calculation for Cart Item Product Combination Condition on having multiple product with same Id.
* Improvement - PHP 8 compatible.
* Improvement - Event advanced_woo_discount_rules_calculate_discount_for_cart_item.
* Fix - BXGY categories are not displaying in report.
* Fix - Warning on applying Cart adjustment.
* Fix - Discount table doesn't loads for variable product based on exclusive option.
* Fix - Not in list filter for variant sku also consider for discount.

= 2.3.12 - 13/01/22 =
* Improvement - WooCommerce 6.1 compatible
* Fix - Error on API calls.

= 2.3.11 - 16/12/21 =
* Improvement - Load cart item quantity through event advanced_woo_discount_rules_cart_item_quantity.
* Improvement - Load cart item count through event advanced_woo_discount_rules_include_cart_item_to_count_quantity.
* Improvement - Disable free shipping rule in statistics.
* Improvement - Event: advanced_woo_discount_rules_admin_rule_notices.
* Improvement - Warning on product doesn't exists / out of stock for BXGY.
* Fix - Purchase history quantities for Specific Product condition doesn't considered variants.
* Fix - Not displaying variant name when having more than 3 combination of attributes.
* Fix - Auto add next available variant when a variant in not purchasable.

= 2.3.10 - 15/11/21 =
* Improvement - Event advanced_woo_discount_rules_calculate_cheapest_discount.
* Improvement - Event advanced_woo_discount_rules_use_sale_badge_percentage_customization.
* Improvement - Revert First order condition doesn't worked for mix guest and login
* Fix - Not displaying variation title in BXGY auto add select on cart.
* Fix - BXGY doesn't auto remove on checkout on rule failed.
* Fix - Handle BXGY sale badge on user group condition.
* Fix - Same price strikeout issue.
* Fix - Fatal error on product object as null.

= 2.3.9 - 21/10/21 =
* Feature - Option to hide other shipping methods when free shipping is available.
* Improvement - Skip invisible variant price for doing strikeout.
* Improvement - Alternative for recalculate discount before apply coupon event.
* Improvement - Taxonomy cache improvement.
* Improvement - Compatible for facebook for woocommerce.
* Improvement - Display discount info based on tax in discount bar.
* Improvement - Removed customize-table.css and added though inline.
* Improvement - Load the awdr_pro.js in footer.
* Improvement - Strikeout price for product addon.
* Improvement - Onsale shortcode index doesn't update on empty value.
* Improvement - Event: advanced_woo_discount_rules_do_process_discounts_of_each_rule.
* Improvement - Event: advanced_woo_discount_rules_coupon_value.
* Improvement - Event: Additional param in advanced_woo_discount_rules_line_item_subtotal_saved_text.
* Fix - First order condition doesn't worked for mix guest and login
* Fix - Handle auto add products based on language while having wpml.
* Fix - Product not auto added when previously added is not in stock.
* Fix - Fatal error while getting product id.
* Fix - Buy X get X not applies while having less quantity.
* Fix - Displaying strikeout in reverse.
* Fix - Wrong discount quantity on some cases in cart.
* Fix - Error while auto add (on product doesn't exists).
* Fix - Free shipping with other exclusive rule.

= 2.3.8 - 22/07/21 =
* Feature - Option to display percentage in sale badge.
* Improvement - Optimize the term query to improve performance.
* Improvement - Option to load minified CSS and JS.
* Improvement - Taxonomy slug support for different language.
* Improvement - Event advanced_woo_discount_rules_dequeue_jquery_ui_datepicker_script.
* Improvement - Event advanced_woo_discount_rules_calculate_current_discount_amount.
* Improvement - Event advanced_woo_discount_rules_is_rule_passed_with_out_discount_for_exclusive_rule.
* Improvement - WPML compatibility for searching filters.
* Improvement - Validation fails for few cases.
* Improvement - Default value for option Apply cart discount as is changed to Coupon.
* Fix - Discount table for bundle product is not showing.
* Fix - Coupon discount values doesn't splits based on filters in backend.
* Fix - Exclude exclusive rule for free products.
* Fix - Same strikeout issue.
* Fix - You save price is displayed wrong while having tax in few cases.
* Fix - Unable to edit rule in mobile.
* Fix - The discount bar is not showing if variant is selected in filter.
* Fix - No coupon label for cart fixed discount.

= 2.3.7 - 30/04/21 =
* Improvement - Moved advance option tab to settings.

= 2.3.6 - 29/04/21 =
* Feature - Recursive option for set discounts.
* Improvement - Reset migration improvement.
* Improvement - Changed rule limit option select box to number field.
* Improvement - Additional param on Event: advanced_woo_discount_rules_get_product_categories
* Improvement - Compatible with PHP 8.
* Improvement - Event: advanced_woo_discount_rules_dynamic_price_html_update
* Improvement - Event: advanced_woo_discount_rules_csv_import_export_separator
* Improvement - Event: advanced_woo_discount_rules_csv_length_for_import.
* Improvement - Event: advanced_woo_discount_rules_discounts_check_bogo_return_values.
* Improvement - Advance option to handle conflict with other plugins.
* Fix - Build onsale list not working while having huge volume of products.
* Fix - Blocking paypal return requests.
* Fix - Subsequent discount with cart rule.
* Fix - Dynamic strikeout shows incorrect value while having multiple rules.

= 2.3.5 - 23/03/21 =
* Feature - Cross sell feature for BXGY cheapest product option.
* Feature - Recipe: sample rules to add.
* Improvement - SKU with product title in product select box.
* Improvement - Subtotal based promotion UI improvement at backend.
* Improvement - Multi site query optimize.
* Improvement - Smart Coupons plugin compatible.
* Improvement - Sale badge translation improvement.
* Improvement - Event: advanced_woo_discount_rules_get_attribute_id_from_taxonomy_name
* Improvement - Event: advanced_woo_discount_rules_exclude_non_stock_product_on_sale_page
* Improvement - Event: advanced_woo_discount_rules_strikeout_price_html additional params.
* Improvement - Translation improvement.
* Fix - Variable product with tag condition not working.
* Fix - The discount bar is not displayed for variable product SKU filter.
* Fix - Show product on sale page even if only the variant has chosen.
* Fix - Report for BXGY cheapest product discount (fixed discount) is not shown.
* Fix - Category select box is not showing all language categories while having WPML.

= 2.3.4 - 01/02/21 =
* Feature - Quantity based promotion message.
* Improvement - Accept additional tags in sale badge.
* Improvement - Translation improvements.
* Improvement - Variable product suffix on strikeout.
* Improvement - Event: advanced_woo_discount_rules_coupon_discount_type_percentage
* Improvement - Event: advanced_woo_discount_rules_apply_coupon_for_products_based_on_filters
* Improvement - Event: advanced_woo_discount_rules_cart_item_key_for_buy_x_get_y_limited_discounts
* Improvement - Event: advanced_woo_discount_rules_select_coupon_from_woocommerce
* Improvement - Email tld condition improvement.
* Fix - Coupon not applied when use settings (disable coupons discount rules will work).
* Fix - Fatal error for child products when category based rule is set.
* Fix - Buy X get Y auto add not added while variant is added in cart.
* Fix - BOGO Cheapest strikeout issue.
* Fix - Last order purchase condition issue
* Fix - Fatal error on view plugin details in plugin page

= 2.3.3 - 16/12/20 =
* Fix - Attribute doesn't loads for few language.
* Fix - Show Discount bar for 0 price product.
* Fix - Duplicate rule is not generated when title has '.
* Fix - Exclusive option for free shipping is not working.
* Fix - Set discount not applies for few cases.
* Fix - Warning message for empty variants.
* Fix - Customer chosen product is not updating for variant together option.
* Improvement - Product addon compatible improvement.
* Improvement - Product addon by themehigh compatible improvement.
* Improvement - Display parent name of category while select child.
* Improvement - Translation for discount label, rule title, Free shipping.
* Improvement - Event: advanced_woo_discount_rules_recalculate_discount_before_apply_coupon.
* Improvement - Calculating tax with fee.
* Improvement - Removing jquery-ui-datepicker script on our pages as it making conflict in few sites.
* Improvement - Improved query for purchase history rules.
* Improvement - Shipping state and country condition.
* Improvement - Event: advanced_woo_discount_rules_set_cheapest_item_key_as_product_id_for_product_page.

= 2.3.2 - 05/11/20 =
* Fix - Displaying discount table if user role based condition is false.
* Fix - Discount fee doesn't works when Combine all discounts option enabled.
* Fix - BOGO Cheapest doesn't works with WPML translation management.
* Fix - BOGO Cheapest individual option not works when apply all matched rule is enabled.
* Improvement - Added nonce for dynamic strikeout ajax method.
* Improvement - Custom Coupon not added in some cases.
* Improvement - Added country field in state condition.
* Improvement - JQuery DragTable updated.
* Improvement - BOGO Cheapest individual option improved.
* Improvement - Optimized the purchase history based first order query.

= 2.3.1 - 16/10/20 =
* Fix - JS error
* Improvement - Discount table improvement.
* Improvement - Product page strikeout doesn't displays while dynamic strikeout option enabled in few cases.
* Improvement - Added new event advanced_woo_discount_rules_user_on_condition_check in pro.

= 2.3.0 - 14/10/20 =
* Feature - Discount table for variants while changing variant options
* Fix - Fatal error because of get_posts method.
* Fix - Tax calculation in discount fee.
* Fix - Wrong discount in BOGO while having apply all matched rules option.
* Fix - Coupon name not displaying in cart.
* Fix - Metorik API not working with Discount Rules.
* Fix - SKU based rule not displays in onsale page.
* Fix - Strikeout doesn't displays when Suppress third party discount plugins option is enabled.
* Improvement - Cart strikeout improvements.
* Improvement - Improved discount info in order meta.
* Improvement - UI for RTL.

= 2.2.2 - 24/09/20 =
* Feature - Apply as coupon option.
* Fix - You saved message not showing on backend order page.
* Fix - Variable product strike through not working with suppress third party option.
* Fix - Disable strikeout when save product through backend inline method.
* Fix - Case sensitive issue on email based rules.
* Fix - Loading multiple fields for custom taxonomy.
* Improvement - Rule not saving when zero prices are entered in bulk discount.
* Improvement - Wholesale price by RymeraWebCo compatible improvement.
* Improvement - Event advanced_woo_discount_rules_hide_specific_rules_in_bulk_table added additional parameter.
* Improvement - Translation for you saved text and applied rule messages.
* Improvement - Disable strikeout when no option is chosen.
* Improvement - BOGO auto add improved.

= 2.2.1 - 08/09/20 =
* Fix - Table column doesn't accepts space.
* Fix - Zipcode condition doesn't works on changing in checkout.
* Improvement - Added nonce while switch version.
* Improvement - Event advanced_woo_discount_rules_change_bulk_rule_quantity while get quantity of products in bulk rule.

= 2.2.0 - 02/09/20 =
* Fix - Security checks improved.
* Fix - Taxonomy not listed in sale page.
* Improvement - Front end script improved.
* Improvement - Disable BXGY auto add when product not in stock.

= 2.1.2 - 22/08/20 =
* Fix - Security fix: though a major release was made in 2.1.0 in August 13th, a few sites might had older versions. In case, they were affected due to cross-site scripting, this fix will clean up the html before displaying in the front end.

= 2.1.1 - 17/08/20 =
* Fix - Country and state field doesn't loads when have multiple
* Fix - Warning while having BOGO rule.
* Improvement - Versioning the script files.

= 2.1.0 - 13/08/20 =
* Fix - Fatal error when WooCommerce is deactivated.
* Fix - Displays only 10 item in sale page for attribute based rules.
* Fix - Disable strikeout if option is disabled for variable products.
* Fix - PHP 7.x warnings.
* Fix - Timeout warnings while checking for update.
* Fix - Warning while having grouped product.
* Improvement - Report based on cart quantities.
* Improvement - Added Aelia Currency Switcher Compatible.
* Improvement - Removed create collate while create table.
* Improvement - Privilege and CSRF check for all requests.
* Improvement - Choose free shipping as default only when applicable.

= 2.0.2 - 29/07/20 =
* Fix - Making conflict with YITH WooCommerce Wishlist
* Fix - Script error because of selectWoo
* Fix - BXGY cheapest individual option calculate wrong discounts
* Improvement - Text improvement

= 2.0.1 - 14/07/20 =
* Fix - Warning on install pack.

= 2.0.0 - 14/07/20 =
* Fix - Cart discount price for single product.
* Fix - Displaying strikeout when chosen apply as coupon.
* Fix - Coupon code not migrated while migration.
* Fix - Not in list condition not working.
* Improvement - Displayed max rule limit reach msg and limits.
* Improvement - select2 to selectWoo.
* Improvement - Individual rule report.
* Improvement - Download exported csv without storing in server.
* Improvement - Report for BOGO rules.
* Feature - Import option.

== Upgrade notice ==