#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Releva.ai Integration Plugin\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-02 15:05+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.6.3; wp-5.9.3\n"
"X-Domain: relevaai"

#. URI of the plugin
#. Author URI of the plugin
msgid "https://releva.ai"
msgstr ""

#: public/class-relevaai-page-token-public.php:156
#: public/class-relevaai-page-token-public.php:180
msgid "I want to subscribe to Marketing emails"
msgstr ""

#. Description of the plugin
msgid ""
"In order to start page views tracking go to Releva Settings Menu in "
"wordpress and add your Access Token, Secret Key, Category Page Token, "
"Product Page Token. To Track page views and cart views you must add your "
"Page tokens on your pages. To do it go to Pages menu and edit the page you "
"want to track. Go to the Releva Pagen Token section and add your Page token "
"there. To ensure the correct page tracking you must synchronise your "
"products catalog from Releva Settings Menu. To be able to visualise "
"correctly the Recommenders returned from Releva in Releva Settings Menu you "
"must add your css selectors. To cusomise how the Recommenders look you can "
"edit the template in public/render-template.php We are using Mustache "
"library for visualization of the templates."
msgstr ""

#. Author of the plugin
msgid "Releva.AI"
msgstr ""

#. Name of the plugin
msgid "Releva.ai Integration Plugin"
msgstr ""
