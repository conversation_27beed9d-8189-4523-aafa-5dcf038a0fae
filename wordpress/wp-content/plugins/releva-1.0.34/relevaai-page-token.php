<?php

/**
 * The plugin bootstrap file
 *
 * This file is read by WordPress to generate the plugin information in the plugin
 * admin area. This file also includes all of the dependencies used by the plugin,
 * registers the activation and deactivation functions, and defines a function
 * that starts the plugin.
 *
 * @link              https://releva.ai
 * @since             1.0.1
 * @package           RelevaAi_Page_Token
 *
 * @wordpress-plugin
 * Plugin Name:       Releva.ai Integration Plugin
 * Plugin URI:        https://releva.ai
 * Description:       In order to start page views tracking go to Releva Settings Menu in wordpress and add your Access Token, Secret Key, Category Page Token, Product Page Token. To Track page views and cart views you must add your Page tokens on your pages. To do it go to Pages menu and edit the page you want to track. Go to the Releva Pagen Token section and add your Page token there. To ensure the correct page tracking you must synchronise your products catalog from Releva Settings Menu. To be able to visualise correctly the Recommenders returned from Releva in Releva Settings Menu you must add your css selectors. To cusomise how the Recommenders look you can edit the template in public/render-template.php We are using Mustache library for visualization of the templates.
 * Version:           1.0.34
 * Author:            Releva.AI
 * Author URI:        https://releva.ai
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       relevaai-page-token
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Currently plugin version.
 * Start at version 1.0.1 and use SemVer - https://semver.org
 * Rename this for your plugin and update it as you release new versions.
 */
define('RELEVAAI_PAGE_TOKEN_VERSION', trim(file_get_contents(plugin_dir_path( __FILE__ ).'/version')));

$path_array  = wp_upload_dir();
$upload_url=$path_array['baseurl'];
define('RelevaAi_Page_Token_DIR', plugin_dir_path( __FILE__ ) );
define('RelevaAi_Page_Token_URI', plugin_dir_url( __FILE__ ) );
define('RelevaAi_Page_Token_UPLOAD_URI', $upload_url);

/**
 * The code that runs during plugin activation.
 * This action is documented in includes/class-relevaai-page-token-activator.php
 */
function activate_relevaai_page_token() {
	require_once plugin_dir_path( __FILE__ ) . 'includes/class-relevaai-page-token-activator.php';
	RelevaAi_Page_Token_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 * This action is documented in includes/class-relevaai-page-token-deactivator.php
 */
function deactivate_relevaai_page_token() {
	require_once plugin_dir_path( __FILE__ ) . 'includes/class-relevaai-page-token-deactivator.php';
	RelevaAi_Page_Token_Deactivator::deactivate();
}

register_activation_hook( __FILE__, 'activate_relevaai_page_token' );
register_deactivation_hook( __FILE__, 'deactivate_relevaai_page_token' );

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-relevaai-page-token.php';

/**
 * Begins execution of the plugin.
 *
 * Since everything within the plugin is registered via hooks,
 * then kicking off the plugin from this point in the file does
 * not affect the page life cycle.
 *
 * @since    1.0.1
 */
function run_relevaai_page_token() {

	$plugin = new RelevaAi_Page_Token();
	$plugin->run();

}
run_relevaai_page_token();
