<?php

/**
 * Define the internationalization functionality
 *
 * Loads and defines the internationalization files for this plugin
 * so that it is ready for translation.
 *
 * @link       https://releva.ai
 * @since      1.0.1
 *
 * @package    RelevaAi_Page_Token
 * @subpackage RelevaAi_Page_Token/includes
 */

/**
 * Define the internationalization functionality.
 *
 * Loads and defines the internationalization files for this plugin
 * so that it is ready for translation.
 *
 * @since      1.0.1
 * @package    RelevaAi_Page_Token
 * @subpackage RelevaAi_Page_Token/includes
 * <AUTHOR>
 */
class RelevaAi_Page_Token_i18n {


  /**
   * Load the plugin text domain for translation.
   *
   * @since    1.0.1
   */
  public function load_plugin_textdomain() {

    load_plugin_textdomain(
      'relevaai',
      false,
      dirname( dirname( plugin_basename( __FILE__ ) ) ) . '/languages/'
    );

  }



}
