<?php

/**
 * Fired during plugin activation
 *
 * @link       https://releva.ai
 * @since      1.0.1
 *
 * @package    RelevaAi_Page_Token
 * @subpackage RelevaAi_Page_Token/includes
 */

/**
 * Fired during plugin activation.
 *
 * This class defines all code necessary to run during the plugin's activation.
 *
 * @since      1.0.1
 * @package    RelevaAi_Page_Token
 * @subpackage RelevaAi_Page_Token/includes
 * <AUTHOR>
 */
class RelevaAi_Page_Token_Activator {

  /**
   * Short Description. (use period)
   *
   * Long Description.
   *
   * @since    1.0.1
   */
  public static function activate() {
    if (!file_exists(ABSPATH . '/releva-service-worker.min.js')) {
      copy(plugin_dir_path( __FILE__ ) . '../public/service-worker.min.js', ABSPATH . '/releva-service-worker.min.js' );
    }
  }

}
