<?php
class RelevaAi_Helper {
  public function to_cart_item($item) {
    $custom = array('string' => array());

    if ($item['variation_id']) {
      $variation = new WC_Product_Variation($item['variation_id']);
      $variation_attributes = $variation->get_attributes();
      foreach($variation_attributes as $attribute_name => $term_slug) {
        $value = get_term_by('slug', $term_slug, $attribute_name)->name;
        $value = $value ? $value : $slug;
        if ($value) {
          $custom['string'][] = array('key' => $attribute_name, 'values' => array($value));
        }
      }
    }

    return array(
      'id' => (string)$item['product_id'],
      'price' => ((float)$item['line_total'] + (float)$item['line_subtotal_tax']) / (int)$item['quantity'],
      'currency' => get_woocommerce_currency(),
      'quantity' => (int)$item['quantity'],
      'custom' => $custom
    );
  }

  public function term_id_to_category_path($term_id)
  {
    $catgory_path = array();
    $parent_category_term_ids = get_ancestors($term_id, 'product_cat');

    foreach($parent_category_term_ids as $parent_term_id) {
      if ($parent_term_id !== $term_id) {
        array_unshift($catgory_path, get_term($parent_term_id)->name);
      }
    }
    array_push($catgory_path, get_term($term_id)->name);
    return implode('/', $catgory_path);
  }

  public function to_releva_product($product)
  {
    $relevaai_request_setting = get_option('relevaai_request_setting');
    $image_size_name = $relevaai_request_setting['recommender_images_size_name'];
    if (!$image_size_name) {
      $image_size_name = 'medium';
    }

    $image_id  = $product->get_image_id();
    $image_url = wp_get_attachment_image_url($image_id, $image_size_name);
    $term_ids = wp_get_post_terms($product->get_id(), 'product_cat', array(
      'fields' => 'ids'
    ));

    $terms_parent_ids = [];
    foreach($term_ids as $term_id) {
      $term_parent_ids = get_ancestors($term_id, 'product_cat', 'taxonomy');
      foreach($term_parent_ids as $parent_term_id) {
        $terms_parent_ids[$parent_term_id] = true;
      }
    }

    $deepest_level_term_ids = array_diff($term_ids, array_keys($terms_parent_ids));
    
    $categories = array();
    foreach($deepest_level_term_ids as $term_id) {
      $categories[] = $this->term_id_to_category_path($term_id);
    }

    $shop_currency = get_woocommerce_currency();
    $productDateCreated = $product->get_date_created();

    $all_attributes = array();

    $custom = array(
      'string' => array(),
      'numeric' => array(
        array(
          "key" => 'stock_quantity',
          "values" => $product->get_stock_quantity() !== null ? array((int)$product->get_stock_quantity()) : array()
        )
      )
    );

    // Get all meta values for the post
    $meta_values = get_post_meta($product->get_id());

    // Loop through the meta values
    foreach ($meta_values as $key => $value) {
      // Uncomment the following lines (92 and 115) to retrieve specific meta fields if you don't want to sync the entire product's meta data
      // Check if the key matches specific values
      //if (in_array($key, ['meta_key1', 'meta_key2', 'meta_key3'])) {
      // $key will contain the meta key, and $value will contain the meta value

      // Get the type of the meta value
      $value_type = gettype($value[0]);

      // Check the value type and cast accordingly
      if ($value_type === 'integer' || $value_type === 'double' || $value_type === 'float') {
        $value[0] = (float) $value[0]; // Cast to numeric (float)
        $value_type = 'numeric';
      } elseif ($value_type === 'object' && $value[0] instanceof DateTime) {
        $value[0] = $value[0]->format(DateTime::ATOM); // Format the DateTime object
        $value_type = 'date';
      } else {
        $value[0] = urldecode((string) $value[0]); // Decode URL-encoded and treat value as string
        $value_type = 'string';
      }

      // Output the meta key, value, and type
      $custom[$value_type][] = array(
        "key" => $key,
        "values" => array($value[0])
      );
      //}
    }

    if ($product->is_type('variable')) {
      $list_price = 0;
      $discount_price = 0;
      $variationsIds = array();

      foreach ($product->get_available_variations() as $variation) {
        /*
        NOTE: we get the list price and discount price from the last variation
        this is fine because the order doesn't matter
        the order doesn't matter, because the prices for all variations are the same
        */
        if (!(new WC_Product_variation($variation['variation_id']))->is_in_stock()) {
          continue;
        }
    
        foreach ($variation['attributes'] as $attribute => $slug) {
          $attribute_name = str_replace('attribute_', '', $attribute);
    
          $value = get_term_by('slug', $slug, $attribute_name)->name;
          $value = $value ? $value : $slug;
          if ($value) {
            if (!array_key_exists($attribute_name, $all_attributes)) {
              $all_attributes[$attribute_name] = array();
            }
            if (!in_array($value, $all_attributes[$attribute_name])) {
              $all_attributes[$attribute_name][] = $value;
            }
          }
        }
        $variationsIds[] = (string) $variation['variation_id'];
      }

      $custom['string'][] = array(
        "key" => 'variationsIds',
        "values" => $variationsIds
      );
      
      $list_price = wc_get_price_including_tax($product, array('price' => (float) $product->get_variation_regular_price()));
      $discount_price = (float) $product->get_variation_sale_price();
    
      $custom['numeric'][] = array(
        "key" => 'variation_price_min',
        "values" =>  array((float)$product->get_variation_price('min')) 
      );
    
      $custom['numeric'][] = array(
        "key" => 'variation_price_max',
        "values" =>  array((float)$product->get_variation_price('max')) 
      );
    
      $custom['numeric'][] = array(
        "key" => 'variation_sale_price_min',
        "values" =>  array((float)$product->get_variation_sale_price('min')) 
      );
    
      $custom['numeric'][] = array(
        "key" => 'variation_sale_price_max',
        "values" =>  array((float)$product->get_variation_sale_price('max')) 
      );
    
      $custom['numeric'][] = array(
        "key" => 'variation_regular_price_min',
        "values" =>  array((float)$product->get_variation_regular_price('min')) 
      );
    
      $custom['numeric'][] = array(
        "key" => 'variation_regular_price_max',
        "values" =>  array((float)$product->get_variation_regular_price('max')) 
      );


    } else {
      $list_price = wc_get_price_including_tax($product, array('price' => $product->get_regular_price()));
      $discount_price = (float) $product->get_sale_price();
    }
    
    if ($discount_price) {
      $discount_price = wc_get_price_including_tax($product, array('price' => $discount_price));
    }

    foreach ($product->get_attributes() as $attribute) {
      $attribute_data = $attribute->get_data();
      /*
      custom attribute example when used in variations
      {
        "id":0,"name":"Fuu","options":["bar","1"],
        "position":2,"visible":true,"variation":true,"is_visible":1,
        "is_variation":1,"is_taxonomy":0,"value":"bar | 1"
      }

      custom attribute example when NOT used in variations
      {
        "id":0,"name":"Fuu","options":["bar","1"],
        "position":2,"visible":true,"variation":false,"is_visible":1,
        "is_variation":0,"is_taxonomy":0,"value":"bar | 1"
      }

      standard attribute example when used in variations
      {
        "id":0,"name":"Fuu","options":["bar","1"],
        "position":2,"visible":true,"variation":false,"is_visible":1,
        "is_variation":0,"is_taxonomy":1,"value":"bar | 1"
      }
      */
      $attribute_name = $attribute_data['name'];
      // we take variation data with priority over general product data
      if (array_key_exists($attribute_name, $all_attributes)) {
        continue;
      }
      foreach ($attribute_data['options'] as $value) {
        $value = $attribute_data['is_taxonomy'] === 1 ? get_term_by('term_id', $value, $attribute_name)->name : $value;
        $all_attributes[$attribute_name][] = $value;
      }
    }

    foreach ($all_attributes as $label => $values) {
      $custom['string'][] = array('key' => $label, 'values' => $values);
    }

    $available = $product->is_in_stock();
    if ($list_price == 0) {
      $available = false;
    }

    $releva_product = array(
      "id" => (string)$product->get_id(),
      "name" => $product->get_name(),
      "description" => $product->get_description(),
      "locale" => explode('-', get_bloginfo('language'))[0],
      "currency" => $shop_currency,
      "listPrice" => $list_price,
      "categories" => $categories,
      "available" => $available,
      "url" => $product->get_permalink(),
      "imageUrl" => $image_url ? $image_url : null,
      "publishedAt" => $productDateCreated ? $productDateCreated->format(DateTime::ATOM) : null,
      "custom" => $custom
    );

    if ($discount_price && $list_price > $discount_price) {
      $releva_product['discountPrice'] = $discount_price;
    }
    return $releva_product;
  }
}
