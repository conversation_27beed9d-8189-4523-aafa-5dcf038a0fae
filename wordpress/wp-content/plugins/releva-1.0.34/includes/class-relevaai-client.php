<?php
class RelevaAi_Client {

  const API_BASE = 'https://releva.ai/api/v0/';
  /**
   * Initialize the client
   *
   * @since    1.0.2
   */
  public function __construct() {
    $relevaai_request_setting = get_option('relevaai_request_setting');
    if ($relevaai_request_setting) {
      $this->access_token = $relevaai_request_setting['access_token'];
      $this->secret_key = $relevaai_request_setting['secret_key'];
    }
  }

  public function subscribe($email) {
     return $this->send_request_with_secret_key('POST', 'subscribe', array('email' => $email));
  }

  public function register($email) {
    return $this->send_request_with_secret_key('POST', 'register', array('email' => $email));
 }

  public function unsubscribe($email) {
     return $this->send_request_with_secret_key('POST', 'unsubscribe', array('email' => $email));
  }

  public function update_products($products) {
     return $this->send_request_with_secret_key('POST', 'products', array('products' => $products));
  }

  public function delete_product($product_id) {
     return $this->send_request_with_secret_key('DELETE', 'products/' . $product_id);
  }

  public function update_carts($carts) {
     return $this->send_request_with_secret_key('POST', 'carts', array('carts' => $carts));
  }

  private function send_request_with_access_token($method, $path, $body = null) {
    return $this->send_request($path, $method, $this->access_token, $body);
  }

  private function send_request_with_secret_key($method, $path, $body = null) {
    return $this->send_request($method, $path, $this->secret_key, $body);
  }

  private function send_request($method, $path, $secret, $body) {
    $curl = curl_init();
    try {
      curl_setopt_array($curl, array(
        CURLOPT_URL => self::API_BASE . $path,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 1,
        CURLOPT_NOSIGNAL => 1,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_POSTFIELDS => $body ? json_encode($body) : null,
        CURLOPT_HTTPHEADER => array(
          'content-type: application/json',
          'Authorization: Bearer ' . $secret
        ),
      ));
      return curl_exec($curl);
    } finally {
      curl_close($curl);
    }
  }
}
