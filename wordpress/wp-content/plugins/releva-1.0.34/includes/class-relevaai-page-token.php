<?php

/**
 * The file that defines the core plugin class
 *
 * A class definition that includes attributes and functions used across both the
 * public-facing side of the site and the admin area.
 *
 * @link       https://releva.ai
 * @since      1.0.0
 *
 * @package    RelevaAi_Page_Token
 * @subpackage RelevaA<PERSON>_Page_Token/includes
 */

/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * Also maintains the unique identifier of this plugin as well as the current
 * version of the plugin.
 *
 * @since      1.0.0
 * @package    RelevaAi_Page_Token
 * @subpackage RelevaAi_Page_Token/includes
 * <AUTHOR>
 */
class RelevaAi_Page_Token
{

  /**
   * The loader that's responsible for maintaining and registering all hooks that power
   * the plugin.
   *
   * @since    1.0.0
   * @access   protected
   * @var      RelevaAi_Page_Token_Loader    $loader    Maintains and registers all hooks for the plugin.
   */
  protected $loader;

  /**
   * The unique identifier of this plugin.
   *
   * @since    1.0.0
   * @access   protected
   * @var      string    $plugin_name    The string used to uniquely identify this plugin.
   */
  protected $plugin_name;

  /**
   * The current version of the plugin.
   *
   * @since    1.0.0
   * @access   protected
   * @var      string    $version    The current version of the plugin.
   */
  protected $version;


  /**
   * The Releva client to use
   *
   * @since    1.0.2
   * @access   protected
   * @var      string    $releva_client   The Releva client to use
   */
  protected $releva_client;

  /**
   * The Releva helper to use
   *
   * @since    1.0.2
   * @access   protected
   * @var      string    $releva_helper   The Releva helper to use
   */
  protected $releva_helper;

  /**
   * Define the core functionality of the plugin.
   *
   * Set the plugin name and the plugin version that can be used throughout the plugin.
   * Load the dependencies, define the locale, and set the hooks for the admin area and
   * the public-facing side of the site.
   *
   * @since    1.0.0
   */
  public function __construct()
  {
    $this->version = RELEVAAI_PAGE_TOKEN_VERSION;
    $this->plugin_name = 'relevaai-page-token';

    $this->load_dependencies();
    $this->set_locale();
    $this->define_admin_hooks();
    $this->define_public_hooks();
  }

  /**
   * Load the required dependencies for this plugin.
   *
   * Include the following files that make up the plugin:
   *
   * - RelevaAi_Page_Token_Loader. Orchestrates the hooks of the plugin.
   * - RelevaAi_Page_Token_i18n. Defines internationalization functionality.
   * - RelevaAi_Page_Token_Admin. Defines all hooks for the admin area.
   * - RelevaAi_Page_Token_Public. Defines all hooks for the public side of the site.
   *
   * Create an instance of the loader which will be used to register the hooks
   * with WordPress.
   *
   * @since    1.0.0
   * @access   private
   */
  private function load_dependencies()
  {

    /**
     * The class responsible for orchestrating the actions and filters of the
     * core plugin.
     */
    require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-relevaai-page-token-loader.php';

    /**
     * The class responsible for defining internationalization functionality
     * of the plugin.
     */
    require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-relevaai-page-token-i18n.php';

    /**
     * The class responsible for defining all actions that occur in the admin area.
     */
    require_once plugin_dir_path(dirname(__FILE__)) . 'admin/class-relevaai-page-token-admin.php';

    /**
     * The class responsible for defining all actions that occur in the public-facing
     * side of the site.
     */
    require_once plugin_dir_path(dirname(__FILE__)) . 'public/class-relevaai-page-token-public.php';


    /**
     * The Releva client
     */
    require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-relevaai-client.php';


    /**
     * The Releva helper methods
     */
    require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-relevaai-helper.php';

    $this->releva_client = new RelevaAi_Client();
    $this->loader = new RelevaAi_Page_Token_Loader();
    $this->releva_helper = new RelevaAi_Helper();
  }

  /**
   * Define the locale for this plugin for internationalization.
   *
   * Uses the RelevaAi_Page_Token_i18n class in order to set the domain and to register the hook
   * with WordPress.
   *
   * @since    1.0.0
   * @access   private
   */
  private function set_locale()
  {

    $plugin_i18n = new RelevaAi_Page_Token_i18n();

    $this->loader->add_action('plugins_loaded', $plugin_i18n, 'load_plugin_textdomain');
  }

  /**
   * Register all of the hooks related to the admin area functionality
   * of the plugin.
   *
   * @since    1.0.0
   * @access   private
   */
  private function define_admin_hooks()
  {

    $plugin_admin = new RelevaAi_Page_Token_Admin($this->get_plugin_name(), $this->get_version(), $this->releva_client, $this->releva_helper);

    $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_styles');
    $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts');
    $this->loader->add_action('admin_menu', $plugin_admin, 'admin_page_token_menu');
    $this->loader->add_action('add_meta_boxes', $plugin_admin, 'page_meta_box');
    $this->loader->add_action('save_post', $plugin_admin, 'page_meta_box_save');


    $this->loader->add_action('woocommerce_update_product', $plugin_admin, 'update_product', 10, 1);

    // NOTE: woocommerce_trash_product doesn't fire on wp 5.9 / woocommerce 6.1.1 not sure why
    $this->loader->add_action('wp_trash_post', $plugin_admin, 'delete_product', 97, 1);
    // NOTE: woocommerce_delete_product doesn't fire on wp 5.9 / woocommerce 6.1.1 not sure why
    $this->loader->add_action('delete_post', $plugin_admin, 'delete_product', 96, 1);
    // Untrash product
    $this->loader->add_action('untrashed_post', $plugin_admin, 'update_product', 10, 1);
  }

  /**
   * Register all of the hooks related to the public-facing functionality
   * of the plugin.
   *
   * @since    1.0.0
   * @access   private
   */
  private function define_public_hooks()
  {

    $plugin_public = new RelevaAi_Page_Token_Public($this->get_plugin_name(), $this->get_version(), $this->releva_client , $this->releva_helper);

    $this->loader->add_action( 'rest_api_init', $plugin_public, 'register_rest_routes', 1);

    $this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_styles');
    $this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_scripts');
    $this->loader->add_action('wp_footer', $plugin_public, 'relevaai_footer');

    $this->loader->add_action('woocommerce_register_form', $plugin_public,'add_subscribe_to_register_form',  9);
    $this->loader->add_action('woocommerce_created_customer', $plugin_public, 'customer_create', 7);

    $this->loader->add_action('woocommerce_edit_account_form', $plugin_public, 'add_subscribe_to_account_settings', 8);
    $this->loader->add_action('woocommerce_save_account_details', $plugin_public, 'update_subscribe_flag_on_account_save', 12, 1);

    $this->loader->add_action('woocommerce_thankyou', $plugin_public, 'order_completed', 1, 1);
  }

  /**
   * Run the loader to execute all of the hooks with WordPress.
   *
   * @since    1.0.0
   */
  public function run()
  {
    $this->loader->run();
  }

  /**
   * The name of the plugin used to uniquely identify it within the context of
   * WordPress and to define internationalization functionality.
   *
   * @since     1.0.0
   * @return    string    The name of the plugin.
   */
  public function get_plugin_name()
  {
    return $this->plugin_name;
  }

  /**
   * The reference to the class that orchestrates the hooks with the plugin.
   *
   * @since     1.0.0
   * @return    RelevaAi_Page_Token_Loader    Orchestrates the hooks of the plugin.
   */
  public function get_loader()
  {
    return $this->loader;
  }

  /**
   * Retrieve the version number of the plugin.
   *
   * @since     1.0.0
   * @return    string    The version number of the plugin.
   */
  public function get_version()
  {
    return $this->version;
  }
}
