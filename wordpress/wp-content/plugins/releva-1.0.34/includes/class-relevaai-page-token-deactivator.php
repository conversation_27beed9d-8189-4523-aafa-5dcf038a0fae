<?php

/**
 * Fired during plugin deactivation
 *
 * @link       https://releva.ai
 * @since      1.0.1
 *
 * @package    RelevaAi_Page_Token
 * @subpackage RelevaAi_Page_Token/includes
 */

/**
 * Fired during plugin deactivation.
 *
 * This class defines all code necessary to run during the plugin's deactivation.
 *
 * @since      1.0.1
 * @package    RelevaAi_Page_Token
 * @subpackage RelevaAi_Page_Token/includes
 * <AUTHOR>
 */
class RelevaAi_Page_Token_Deactivator {

  /**
   * Short Description. (use period)
   *
   * Long Description.
   *
   * @since    1.0.1
   */
  public static function deactivate() {
    unlink(ABSPATH . '/releva-service-worker.min.js');
  }

}
