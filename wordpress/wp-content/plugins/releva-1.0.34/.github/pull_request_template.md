### Description
Please briefly explain what this PR does.

This PR is (select one):
- [ ] planned - [RN-XXX](https://releva.atlassian.net/browse/RN-XXX)
- [ ] unplanned - explain below

**Important: PR will be rejected unless this section is filled out**


### Testing Checklist
Please test all of the following scenarios and check the relevant boxes
  - frontend
    - [ ] tracking
      - [ ] home page (need to add custom token)
      - [ ] category page - with selected category, ~and product ids~
      - [ ] product page - with product id
      - [ ] cart page (need to add custom token)
      - [ ] checkout succcess page (need to add custom token)
      - [ ] with woocommerce deactivated
    - [ ] track selected custom fields in cart
    - [ ] track profile when logged in
    - [ ] track profile.email during guest checkout
    - [ ] recommendations
  - backend
    - [ ] track catalog updates (on product update from store admin)
      - [ ] (incl. bulk update)
      - [ ] (incl. changes to variations)
    - [ ] track cart paid
      - [ ] incl. selected custom fields
    - [ ] track catalog custom fields
    - [ ] ensure out of stock is propagated to releva on last stock item purchased
    - [ ] product move to trash (incl. bulk move to trash)
    - [ ] product restore from trash (incl. bulk restore)
    - [ ] product delete (incl. bulk delete)
    - [ ] /releva/integration/products API
      - [ ] with woocommerce deactivated
    - [ ] /releva/integration/environment API
      - [ ] with woocommerce deactivated
    - [ ] register
    - [ ] subscribe on register
    - [ ] subscribe on account details page
    - [ ] unsubscribe on account details page


**Important: PR will be rejected unless this section is filled out**

### Pre-merge Checklist
- [ ] any PRs that this PR depends on have been merged and deployed
- [ ] any new or updated APIs have been documented (paste links below)
- [ ] the version that the module reports has been changed to (enter value)
  - [ ] in the `version` file
  - [ ] in the module comment in `relevaai-page-token.php`
  - [ ] `Changelog.md` has been updated with an entry with the new version and its higlights
- [ ] any new functionality is added to the to the testing checklist in the pull request template
- [ ] any new functionality is added to the testing checklist above
