<?php

/**
 * Provide a admin area view for the plugin
 *
 * This file is used to markup the admin-facing aspects of the plugin.
 *
 * @link       https://releva.ai
 * @since      1.0.1
 *
 * @package    RelevaAi_Page_Token
 * @subpackage RelevaAi_Page_Token/admin/partials
 */
?>

<!-- This file should primarily consist of HTML with a little bit of PHP. -->
<div class="wrap">
    <div class="relevaai_card">
        <div class="relevaai_card-header" style="font-size: 15px;">
            <strong>Configuration</strong>
        </div>
        <div class="relevaai_card-body">
            <form class="form-horizontal" action="" method="post">
                <div class="relevaai-form-group">
                    <strong>Authentication</strong>
                    <hr/>
                    <div class="row">
                        <div class="relevaai_col-md-6">
                            <label class="control-label" for="">Access token:</label>
                            <input type="text" name="relevaai_request_setting[access_token]" class="relevaai-form-control" value="<?php if(array_key_exists('access_token', $relevaai_request_setting)) { echo $relevaai_request_setting['access_token']; } ?>">
                            <p>You can find your access token in <a target="_blank" rel="noopener noreferrer" href="https://releva.ai/admin">Releva</a> by navigating to the Settings -> Shop page.</p>
                        </div>
                        <div class="relevaai_col-md-6">
                            <label class="control-label" for="">Secret key:</label>
                            <input type="text" name="relevaai_request_setting[secret_key]" class="relevaai-form-control" value="<?php if(array_key_exists('secret_key', $relevaai_request_setting)) {echo $relevaai_request_setting['secret_key'];} ?>">
                            <p>You can find your secret key in  <a target="_blank" rel="noopener noreferrer" href="https://releva.ai/admin">Releva</a> by navigating to the Settings -> Shop page.</p>
                        </div>
                    </div>
                    <?php if (class_exists('WooCommerce')): ?>
                      <br/><br/>
                      <strong>Page Tokens</strong>
                      <hr/>
                      <div class="row">
                          <div class="relevaai_col-md-6">
                              <label class="control-label" for="">Home Page Token:</label>
                              <input type="text" name="relevaai_request_setting[home_page_token]" class="relevaai-form-control" value="<?php if(array_key_exists('home_page_token', $relevaai_request_setting)) {echo $relevaai_request_setting['home_page_token'];} ?>">
                              <p>You can find this the corresponding page token in  <a target="_blank" rel="noopener noreferrer" href="https://releva.ai/admin">Releva</a> by navigating to the Recommenders page.</p>
                          </div>
                          <div class="relevaai_col-md-6">
                              <label class="control-label" for="">Category Pages Token:</label>
                              <input type="text" name="relevaai_request_setting[category_page_token]" class="relevaai-form-control" value="<?php if(array_key_exists('category_page_token', $relevaai_request_setting)) {echo $relevaai_request_setting['category_page_token'];} ?>">
                              <p>You can find this the corresponding page token in  <a target="_blank" rel="noopener noreferrer" href="https://releva.ai/admin">Releva</a> by navigating to the Recommenders page.</p>
                          </div>
                          <div class="relevaai_col-md-6">
                              <label class="control-label" for="">Product Pages Token:</label>
                              <input type="text" name="relevaai_request_setting[product_page_token]" class="relevaai-form-control" value="<?php if (array_key_exists('product_page_token', $relevaai_request_setting)) {echo $relevaai_request_setting['product_page_token'];} ?>">
                              <p>You can find this the corresponding page token in  <a target="_blank" rel="noopener noreferrer" href="https://releva.ai/admin">Releva</a> by navigating to the Recommenders page.</p>
                          </div>
                          <div class="relevaai_col-md-6">
                              <label class="control-label" for="">Cart Page Token:</label>
                              <input type="text" name="relevaai_request_setting[cart_page_token]" class="relevaai-form-control" value="<?php if (array_key_exists('cart_page_token', $relevaai_request_setting)) {echo $relevaai_request_setting['cart_page_token'];} ?>">
                              <p>You can find this the corresponding page token in  <a target="_blank" rel="noopener noreferrer" href="https://releva.ai/admin">Releva</a> by navigating to the Recommenders page.</p>
                          </div>
                          <div class="relevaai_col-md-6">
                              <label class="control-label" for="">Search Page Token:</label>
                              <input type="text" name="relevaai_request_setting[search_page_token]" class="relevaai-form-control" value="<?php if (array_key_exists('search_page_token', $relevaai_request_setting)) {echo $relevaai_request_setting['search_page_token'];} ?>">
                              <p>You can find this the corresponding page token in  <a target="_blank" rel="noopener noreferrer" href="https://releva.ai/admin">Releva</a> by navigating to the Recommenders page.</p>
                          </div>
                      </div>
                      <br/><br/>
                      <strong>Recommender Render Settings</strong>
                      <hr/>
                      <div class="row">
                          <div class="relevaai_col-md-6">
                              <label class="control-label" for="">Recommender images size name:</label>
                              <input type="text" name="relevaai_request_setting[recommender_images_size_name]" class="relevaai-form-control" value="<?php if (array_key_exists('recommender_images_size_name', $relevaai_request_setting)) {echo $relevaai_request_setting['recommender_images_size_name'];} ?>">
                              <p>If not set it will default to "medium".</p>
                          </div>
                          <div class="relevaai_col-md-6">
                              <label class="control-label" for="">Recommender element margin right:</label>
                              <input type="text" name="relevaai_request_setting[recommender_element_mr]" class="relevaai-form-control" value="<?php if (array_key_exists('recommender_element_mr', $relevaai_request_setting)) {echo $relevaai_request_setting['recommender_element_mr'];} ?>">
                              <p><strong>This setting applies only for manual rendering.</strong> If not set it will be 3%.</p>
                          </div>
                    </div>
                    <div class="row">
                          <div class="relevaai_col-md-6">
                              <label class="control-label" for="">Recommender element margin bottom:</label>
                              <input type="text" name="relevaai_request_setting[recommender_element_mb]" class="relevaai-form-control" value="<?php if (array_key_exists('recommender_element_mb', $relevaai_request_setting)) {echo $relevaai_request_setting['recommender_element_mb'];} ?>">
                              <p><strong>This setting applies only for manual rendering.</strong> If not set it will be 4em</p>
                          </div>
                          <div class="relevaai_col-md-6">
                              <label class="control-label" for="">Recommender element width:</label>
                              <input type="text" name="relevaai_request_setting[recommender_element_width]" class="relevaai-form-control" value="<?php if (array_key_exists('recommender_element_width', $relevaai_request_setting)) {echo $relevaai_request_setting['recommender_element_width'];} ?>">
                              <p><strong>This setting applies only for manual rendering.</strong> If not set it will be 29%.</p>
                          </div>
                      </div>
                      <br/><br/>
                    <?php endif ?>
                    <strong>Privacy and Data Protection</strong>
                      <hr/>
                      <div class="row">
                          <div class="relevaai_col-md-6">
                              <label class="control-label" for="">Consent cookie name:</label>
                              <input type="text" name="relevaai_request_setting[consent_cookie_name]" class="relevaai-form-control" value="<?php if (array_key_exists('consent_cookie_name', $relevaai_request_setting)) {echo $relevaai_request_setting['consent_cookie_name'];} ?>">
                              <p>The name of the cookie, which, if present, indicates that the visior has agreed for their data to be collected and processed for the purposes of analytics and first-level marketing.</p>
                          </div>
                          <div class="relevaai_col-md-6">
                              <label class="control-label" for="">Conent cookie value:</label>
                              <input type="text" name="relevaai_request_setting[consent_cookie_value]" class="relevaai-form-control" value="<?php if (array_key_exists('consent_cookie_value', $relevaai_request_setting)) {echo $relevaai_request_setting['consent_cookie_value'];} ?>">
                              <p>Please indicate the value of the cookie, which, if matching, indicates that the visior has agreed for their data to be collected and processed for the purposes of analytics and first-level marketing. Leave this blank to only check for cookie presence.</p>
                          </div>
                    </div>
                    <br/><br/>
                    <strong>Web Push Notifications</strong>
                    <hr/>
                    <div class="row">
                        <div class="relevaai_col-md-6">
                            <label class="control-label" for="">Enable web push</label>
                            <input type="checkbox" name="relevaai_request_setting[enable_push_notifications]" class="relevaai-form-control" <?php if (array_key_exists('enable_push_notifications', $relevaai_request_setting) && isset($relevaai_request_setting['enable_push_notifications'])) { echo 'checked="checked"'; } ?>>
                            <p>Checking this box allows Releva to collect permissions for and send push notifications.</p>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <hr>
                </div>
                <div class="form-group">
                    <div class="relevaai_col-sm-8">
                        <button type="submit" name="save_settings_info" class="button button-primary button-large">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>



<?php
if (class_exists('WooCommerce')) {
?>
  <div class="wrap">
      <div class="relevaai_card">
          <div class="relevaai_card-header" style="font-size: 15px;">
              <strong>Schedule Product Sync</strong>
          </div>
          <div class="relevaai_card-body">
              <div class="row">
                  <div class="relevaai_col-md-12">
                      <p>Please click the button below to synchronize your catalogue with Releva.</p>
                      <button <?php if (!array_key_exists('secret_key', $relevaai_request_setting) || !$relevaai_request_setting['secret_key']) {echo 'disabled="disabled"';} ?>
                        class="button button-primary button-large"
                        onclick="window.location.href = 'admin.php?page=page-token&scheduleProductSync=1';">Sync Now</button>
                      <?php
                        if (isset($_GET['scheduleProductSync']) && array_key_exists('secret_key', $relevaai_request_setting) && $relevaai_request_setting['secret_key']) {
                          $curl = curl_init();
                          try {
                            curl_setopt_array($curl, array(
                                CURLOPT_URL => 'https://releva.ai/api/v0/products/sync',
                                CURLOPT_RETURNTRANSFER => true,
                                CURLOPT_ENCODING => '',
                                CURLOPT_MAXREDIRS => 10,
                                CURLOPT_TIMEOUT => 1,
                                CURLOPT_NOSIGNAL => 1,
                                CURLOPT_FOLLOWLOCATION => true,
                                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                CURLOPT_CUSTOMREQUEST => 'POST',
                                CURLOPT_HTTPHEADER => array(
                                    'content-type: application/json',
                                    'Authorization: Bearer ' . $relevaai_request_setting['secret_key']
                                ),
                            ));

                            $response = curl_exec($curl);
                          } finally {
                            curl_close($curl);
                          }
                          ?><script type="text/javascript">
                            jQuery(document).ready(function() {
                              Swal.fire({
                                icon: 'success',
                                title: 'Sync scheduled successfully.',
                                text: '',
                                showConfirmButton: false,
                                timer: 1600
                              });
                            });
                          </script><?php
                        } else if (!array_key_exists('secret_key', $relevaai_request_setting) || !$relevaai_request_setting['secret_key']) {
                          ?><p>Please add your secret key first.</p><?php
                        }
                      ?>
                  </div>
              </div>
          </div>
      </div>
  </div>
<?php
}
?>
