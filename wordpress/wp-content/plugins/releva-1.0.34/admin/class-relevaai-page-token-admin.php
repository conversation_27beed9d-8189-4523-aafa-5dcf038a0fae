<?php


/**
 * The admin-specific functionality of the plugin.
 *
 * @link       https://releva.ai
 * @since      1.0.1
 *
 * @package    RelevaAi_Page_Token
 * @subpackage RelevaAi_Page_Token/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the admin-specific stylesheet and JavaScript.
 *
 * @package    RelevaAi_Page_Token
 * @subpackage RelevaAi_Page_Token/admin
 * <AUTHOR>
 */
class RelevaAi_Page_Token_Admin {

  /**
   * The ID of this plugin.
   *
   * @since    1.0.1
   * @access   private
   * @var      string    $plugin_name    The ID of this plugin.
   */
  private $plugin_name;

  /**
   * The version of this plugin.
   *
   * @since    1.0.1
   * @access   private
   * @var      string    $version    The current version of this plugin.
   */
  private $version;

  /**
   * Initialize the class and set its properties.
   *
   * @since    1.0.1
   * @param      string    $plugin_name       The name of this plugin.
   * @param      string    $version    The version of this plugin.
   */

   /**
    * The Releva client to use
    *
    * @since    1.0.1
    * @access   protected
    * @var      string    $releva_client   The Releva client to use
    */
   protected $releva_client;

   /**
    * The Releva helper to use
    *
    * @since    1.0.1
    * @access   protected
    * @var      string    $releva_helper   The Releva helper to use
    */
   protected $releva_helper;

  public function __construct( $plugin_name, $version, $releva_client, $releva_helper) {

    $this->plugin_name = $plugin_name;
    $this->version = $version;
    $this->releva_client = $releva_client;
    $this->releva_helper = $releva_helper;
  }

  public function delete_product($product_id) {
    // NOTE: we use the generic wordpress hook for post trash/delete
    // because woocommerce_trash_product/woocommerce_delete_product do not fire on wp 5.9/wc 6.1.1
    // hence we need this additional check here
    if (get_post_type($product_id) !== 'product') {
      return;
    }

    $this->releva_client->delete_product($product_id);
  }

  /**
   * Updates a single releva product.
   *
   * @since    1.0.1
   */
  public function update_product($product_id) {
    // NOTE: we use the generic wordpress hook for post untrash
    // because woocommerce_untrash_product do not fire on wp 5.9/wc 6.1.1
    // hence we need this additional check here
    if (get_post_type($product_id) !== 'product' || get_post_status($product_id) === 'trash') {
      return;
    }

    $product = wc_get_product($product_id);
    if (!$product) {
      return;
    }

    $this->releva_client->update_products(array($this->releva_helper->to_releva_product($product)));
  }

  /**
   * Register the stylesheets for the admin area.
   *
   * @since    1.0.1
   */
  public function enqueue_styles() {
    wp_enqueue_style( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'css/relevaai-page-token-admin.css', array(), $this->version, 'all' );
  }

  /**
   * Register the JavaScript for the admin area.
   *
   * @since    1.0.1
   */
  public function enqueue_scripts() {
    wp_enqueue_script( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'js/sweetalert.min.js', array( 'jquery' ), $this->version, false );
    wp_enqueue_script( $this->plugin_name, plugin_dir_url( __FILE__ ) . 'js/relevaai-page-token-admin.js', array( 'jquery' ), $this->version, false );
  }

  public function admin_page_token_menu() {
    add_menu_page('Releva.AI', 'Releva.AI', 'manage_options', 'page-token',  array($this, 'relevaai_page_token_menu'),'dashicons-admin-settings',5);
  }
  public function relevaai_page_token_menu() {
    global $wpdb;
    $tdate = date('Y-m-d H:m:s');
    if (isset($_POST['save_settings_info'])) {
      update_option('relevaai_request_setting',$_POST['relevaai_request_setting']);
      ?>
        <script type="text/javascript">
          jQuery(document).ready(function() {
            Swal.fire({
              icon: 'success',
              title: 'Saved successfully',
              text: '',
              showConfirmButton: false,
              timer: 1600
            });
          });
        </script>
    <?php
    }
    $relevaai_request_setting = get_option('relevaai_request_setting');
    if ($relevaai_request_setting === false) {
      $relevaai_request_setting = array();
    }
    require_once RelevaAi_Page_Token_DIR . 'admin/partials/relevaai-page-token-admin-display.php';
  }


  public function page_meta_box() {
      add_meta_box('page_meta_box',__( 'Releva Page Token', $this->plugin_name ),array($this,'page_meta_box_content') ,'page','normal','low');
  }
  public function page_meta_box_content( $post ) {
    wp_nonce_field( plugin_basename( __FILE__ ), 'page_meta_box_content_nonce' );
    $post->ID;
    $relevaai_page_token = get_post_meta($post->ID, 'relevaai_page_token', true );
    $relevaai_event_tracking = get_post_meta($post->ID, 'relevaai_event_tracking', true );
    ?>
    <div class="relevaai-form-group">
      <div class="row">
        <div class="relevaai_col-md-12">
              <div class="relevaai_col-md-6">
                <label class="control-label" for="name">Page Token:</label>
                <input type="text" style="width: 100%;" name="relevaai_page_token" value="<?php echo $relevaai_page_token; ?>" placeholder="Page Token" class="relevaai-form-control">
              </div>
            </div>
      </div>
    </div>
    <div class="relevaai-form-group">
      <div class="row">
        <div class="relevaai_col-md-12">
          <div class="relevaai_col-md-10">
                <label class="control-label" for="name">Custom Event Tracking:</label>
                <textarea class="relevaai-form-control" name="relevaai_event_tracking" ><?php echo $relevaai_event_tracking; ?></textarea>
              </div>
        </div>
      </div>
    </div>

    <?php
  }

  public function page_meta_box_save( $post_id ) {
    if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE )
      return;

    if (!array_key_exists('page_meta_box_content_nonce', $_POST) || !wp_verify_nonce( $_POST['page_meta_box_content_nonce'], plugin_basename( __FILE__ ) ) )
      return;

    if ( 'page' == $_POST['post_type'] ) {
      if ( !current_user_can( 'edit_page', $post_id ) )
        return;
    } else {
      if ( !current_user_can( 'edit_post', $post_id ) )
        return;
    }
    update_post_meta($post_id, 'relevaai_page_token', $_POST['relevaai_page_token']);
    update_post_meta($post_id, 'relevaai_event_tracking', $_POST['relevaai_event_tracking']);
  }

}
