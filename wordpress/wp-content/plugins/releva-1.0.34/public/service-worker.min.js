// Register event listener for the 'push' event.
self.addEventListener("push",function(a){// Retrieve the textual payload from event.data (a PushMessageData object).
// Other formats are supported (ArrayBuffer, Blob, JSON), check out the documentation
// on https://developer.mozilla.org/en-US/docs/Web/API/PushMessageData.
const{title:b,...c}=a.data?a.data.json():{};// Keep the service worker alive until the notification is created.
"releva"===c.source&&a.waitUntil(// Show a notification
self.registration.showNotification(b,c))}),self.addEventListener("notificationclick",function(a){a.notification.close(),a.waitUntil(this.clients.openWindow(a.notification.data.url))});
