<?php

/**
 * The public-facing functionality of the plugin.
 *
 * @link       https://releva.ai
 * @since      1.0.0
 *
 * @package    RelevaAi_Page_Token
 * @subpackage RelevaAi_Page_Token/public
 */
/**
 * The public-facing functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the public-facing stylesheet and JavaScript.
 *
 * @package    RelevaAi_Page_Token
 * @subpackage RelevaAi_Page_Token/public
 * <AUTHOR>
 */
class RelevaAi_Page_Token_Public
{
    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;
    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;
    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param      string    $plugin_name       The name of the plugin.
     * @param      string    $version    The version of this plugin.
     */


     /**
      * The Releva client to use
      *
      * @since    1.0.2
      * @access   protected
      * @var      string    $releva_client   The Releva client to use
      */
     protected $releva_client;


     /**
      * The Releva helper to use
      *
      * @since    1.0.2
      * @access   protected
      * @var      string    $releva_helper   The Releva helper to use
      */
     protected $releva_helper;

    public function __construct( $plugin_name, $version, $releva_client, $releva_helper) {

      $this->plugin_name = $plugin_name;
      $this->version = $version;
      $this->releva_client = $releva_client;
      $this->releva_helper = $releva_helper;
    }


    public function register_rest_routes() {
      register_rest_route( 'releva/integration', '/environment', [
        'method'   => WP_REST_Server::READABLE,
        'callback' => function($request) {
          $response = array(
            'integration' => array(
              'version' => RELEVAAI_PAGE_TOKEN_VERSION
            ),
            'php' => array(
              'version' => phpversion()
            ),
            'platform' => array(
              'family' => 'wordpress',
              'version' => get_bloginfo('version'),
              'woocommerce_version' => defined('WC_VERSION') ? WC_VERSION : null
            )
          );

          return rest_ensure_response($response);
        },
      ]);


      register_rest_route( 'releva/integration', '/products', [
        'method'   => WP_REST_Server::READABLE,
        'callback' => function($request) {
          try {
              $per_page = 20;

              $total_number_of_products = new WP_Query(array(
                'post_type' => 'product',
                'post_status' => 'publish',
                'posts_per_page' => 1
              ));

              $total_pags = ceil($total_number_of_products->found_posts / $per_page);
              $page = $request->get_param( 'page' );

              $releva_products = array();
              if (class_exists('WooCommerce')) {
                $products = wc_get_products(array('status' => 'publish', 'page' => (int) $page, 'limit' => $per_page));
                foreach ($products as $product) {
                  $releva_products[] = $this->releva_helper->to_releva_product($product);
                }
              }

              return rest_ensure_response(array('products' => $releva_products, 'pageCount' => $total_pags));
            } finally {
                wp_reset_query();
                wp_reset_postdata();
            }
        },
        'permission_callback' => function($request) {
          $headers = getallheaders();
          $relevaai_request_setting = get_option('relevaai_request_setting');
          $auhtorization = $headers['Authorization'] ?  $headers['Authorization'] : $headers['authorization'];

          if($auhtorization === "Bearer ". $relevaai_request_setting['secret_key']) {
            return true;
          } else {
            return false;
          }
        },
        'args'     => [
          'page' => [
            'required' => true,
            'type'     => 'integer',
          ],
        ],
      ]);
    }

    public function add_subscribe_to_register_form() {
        woocommerce_form_field('releva_subscribe', array(
            'type'          => 'checkbox',
            'class'         => array('form-row privacy'),
            'label_class'   => array('woocommerce-form__label woocommerce-form__label-for-checkbox checkbox'),
            'input_class'   => array('woocommerce-form__input woocommerce-form__input-checkbox input-checkbox'),
            'required'      => false,
            'label'         => __('I want to subscribe to Marketing emails', 'relevaai'),
        ));
    }

    public function customer_create($customer_id)
    {
      $user = get_user_by('id', $customer_id);
      if ($user) {
        if (isset($_POST['releva_subscribe'])) {
            update_user_meta($customer_id, 'releva_subscribe_set', sanitize_text_field(1));
            $this->releva_client->subscribe($user->user_email);
        }
        $this->releva_client->register($user->user_email);
      }
    }

    public function add_subscribe_to_account_settings()
    {
          $current_user = wp_get_current_user();
          $current_user_id = $current_user->ID;
          $user = get_user_by('id', $current_user_id);
          $data = get_user_meta($current_user_id);
          ?>
          <p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
              <label for="releva_subscribe"><?php _e('I want to subscribe to Marketing emails', 'relevaai'); ?></label>
              <input type="checkbox" class="" name="releva_subscribe" id="releva_subscribe" <?php echo ((bool)$data['releva_subscribe'][0]) ? 'checked' : ''; ?> />
          </p>
      <?php
    }


    public function order_completed($order_id)
    {
      if (!get_post_meta($order_id, '_releva_thankyou_action_done', true)) {
          $order = new WC_Order($order_id);
          $carts = array(
            array(
              'products' => array(),
              'orderId' => (string) $order_id,
              'cartPaid' => true,
              'email' => $order->billing_email
            )
          );
          foreach ($order->get_items() as $item) {
            $carts[0]['products'][] = $this->releva_helper->to_cart_item($item);
          }

          $this->releva_client->update_carts($carts);
          // Flag the action as done (to avoid repetitions on reload for example)
           $order->update_meta_data( '_releva_thankyou_action_done', true );
           $order->save();
        }
    }


    public function update_subscribe_flag_on_account_save($user_id)
    {
        if (isset($_POST['releva_subscribe'])) {
            update_user_meta($user_id, 'releva_subscribe', sanitize_text_field(1));
            $user = get_user_by('id', $user_id);
            $this->releva_client->subscribe($user->user_email);
        }
        if (!isset($_POST['releva_subscribe'])) {
            update_user_meta($user_id, 'releva_subscribe', sanitize_text_field(0));
            $user = get_user_by('id', $user_id);
            $this->releva_client->unsubscribe($user->user_email);
        }
    }

    /**
     * Register the stylesheets for the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function enqueue_styles()
    {
        wp_enqueue_style($this->plugin_name, plugin_dir_url(__FILE__) . 'css/relevaai-page-token-public.css', array(), $this->version, 'all');
    }
    /**
     * Register the JavaScript for the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts()
    {
        wp_enqueue_script($this->plugin_name, plugin_dir_url(__FILE__) . 'js/relevaai-page-token-public.js', array('jquery'), $this->version, false);
    }


    public function relevaai_footer() {
        if (class_exists('WooCommerce')) {
            if (is_shop()) {
                $relevaai_post_id = get_option('woocommerce_shop_page_id');
            } else {
                $relevaai_post_id = get_the_ID();
            }
        } else {
            $relevaai_post_id = get_the_ID();
        }

        $current_user = wp_get_current_user();
        $user_first_name = esc_html($current_user->user_firstname);
        $user_last_name = esc_html($current_user->user_lastname);
        $user_email = esc_html($current_user->user_email);
        $relevaai_request_setting = get_option('relevaai_request_setting');
        $access_token = $relevaai_request_setting['access_token'];
        $secret_key = $relevaai_request_setting['secret_key'];
        $home_page_token = $relevaai_request_setting['home_page_token'];
        $category_page_token = $relevaai_request_setting['category_page_token'];
        $product_page_token = $relevaai_request_setting['product_page_token'];
        $cart_page_token = $relevaai_request_setting['cart_page_token'];
        $search_page_token = $relevaai_request_setting['search_page_token'];
        $relevaai_page_token = get_post_meta($relevaai_post_id, 'relevaai_page_token', true);
        $relevaai_event_tracking = get_post_meta($relevaai_post_id, 'relevaai_event_tracking', true);

        $recommender_element_mr = '3%';
        if ($relevaai_request_setting['recommender_element_mr']){
            $recommender_element_mr = $relevaai_request_setting['recommender_element_mr'];
        }
        $recommender_element_mb = '4em';
        if ($relevaai_request_setting['recommender_element_mb']){
            $recommender_element_mb = $relevaai_request_setting['recommender_element_mb'];
        }
        $recommender_element_width = '29%';
        if ($relevaai_request_setting['recommender_element_width']){
            $recommender_element_width = $relevaai_request_setting['recommender_element_width'];
        }

        ?>
        <script nitro-exclude>
            window.relevaPushCallback = function(results) {
                <?php
                  if ($relevaai_event_tracking) {
                    echo $relevaai_event_tracking;
                  }
                ?>
            };

            window.relevaWpNonce = '<?php echo wp_create_nonce(); ?>';

            window.relevaErrorCallback = function(error) {
                console.error(`Encountered Releva error: ${error.message}`);
            };

            window.relevaPushObject = <?php echo json_encode(
              array('page' => array('locale' => explode('-', get_bloginfo('language'))[0]))
            ); ?>
        </script>
    <?php

        if ($user_email) {
          ?>
            <script nitro-exclude>
              window.relevaPushObject.profile = <?php echo json_encode(
                array('firstName' => $user_first_name, 'lastName' => $user_last_name, 'email' => $user_email)
              ); ?>;
            </script>
          <?php
        }

        $page_token = $relevaai_page_token;

        if (class_exists('WooCommerce')) {
        // $product is always defined because so we need to also check if we ar on the product page
        if (is_product()) {
          global $product;
          if ($product) {
            ?>
              <script nitro-exclude>
                window.relevaPushObject.product = <?php echo json_encode(array('id' => (string) $product->get_id())); ?>;
              </script>
            <?php
          }
        }

          if (is_product() && $product_page_token) {
              $page_token = $product_page_token;
          } else if (is_order_received_page()) {
            $order_id = wc_get_order_id_by_order_key($_GET['key']);

            if ($order_id) {
              $order = new WC_Order($order_id);
              $items = $order->get_items();

              $cart_products = array();
              foreach ($items as $item) {
                $cart_products[] = $this->releva_helper->to_cart_item($item);
              }

              ?>
                <script nitro-exclude>
                  window.relevaPushObject.cart = <?php echo json_encode(
                    array('products' => $cart_products)
                  ); ?>;

                  // NOTE: this fixes an issue observed on wordpress 5.9.3 and 6.1.1 in combination with woo 7.2.2
                  // where the profile object is not set on guest checkout
                  // this ensures that if all else fails, it is set from the order's billing address
                  if (!window.relevaPushObject.profile) {
                    window.relevaPushObject.profile = <?php echo json_encode(
                      array(
                        'firstName' => $order->get_billing_first_name(), 
                        'lastName' => $order->get_billing_last_name(), 
                        'email' => $order->get_billing_email()
                      )
                    ); ?>;
                  }

                </script>
              <?php
            }
          } else if (is_product_category()) {
            $page_token = $category_page_token;
            $category = get_queried_object();
            $terms = $category->term_id;
            $term_name = get_term( $terms )->name;
            $category = $term_name;
            $catgory_path = array();
            $parentcats = get_ancestors($terms, 'product_cat');

            foreach($parentcats as $parentcat) {
                $term_name = get_term($parentcat)->name;
                array_unshift($catgory_path, $term_name);
            }
            array_push($catgory_path, $category);
            $string_version = implode('/', $catgory_path);
            ?>
              <script nitro-exclude>
                window.relevaPushObject.page.categories = ['<?php echo $string_version; ?>'];
              </script>
            <?php
          } else if (is_front_page()) {
            $page_token = $home_page_token;
          } else if (is_cart()) {
            $page_token = $cart_page_token;
          } else if (is_search() && isset($_GET['s'])) {
            $page_token = $search_page_token;
            ?>
              <script nitro-exclude>
                  window.relevaPushObject.page.query = '<?php echo $_GET['s']; ?>';
              </script>
            <?php
          }
        }

        ?>
          <script nitro-exclude>
            window.relevaPushObject.page.token = '<?php echo $page_token; ?>' || undefined;
          </script>
        <?php


        $cart_products = array();
        if (class_exists('WooCommerce')) {
          if (WC()->cart) {
            foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
              $cart_products[] = $this->releva_helper->to_cart_item($cart_item);
            }
          }
        }

        if (count($cart_products) > 0) {
          ?>
            <script nitro-exclude>
              window.relevaPushObject.cart = <?php echo json_encode(array('products' => $cart_products)); ?>;
            </script>
          <?php
        }

        if (isset($relevaai_request_setting['enable_push_notifications'])) {
          ?>
            <script nitro-exclude>
              window.relevaServiceWorkerUrl = '/releva-service-worker.min.js';
            </script>
          <?php
        }

        $current_currency;
        if (has_filter('wcml_price_currency')) {
          $current_currency = apply_filters('wcml_price_currency', NULL);

          if ($current_currency) {
            ?>
              <script nitro-exclude>
                window.relevaPushObject.page.currency = '<?php echo $current_currency; ?>';
              </script>
            <?php
          }
        } else {
          $current_currency = get_woocommerce_currency();

          if ($current_currency) {
            ?>
              <script nitro-exclude>
                window.relevaPushObject.page.currency = '<?php echo $current_currency; ?>';
              </script>
            <?php
          }
        }

        


        ?>
        <script nitro-exclude>
            document.addEventListener('relevaSdkLoaded', function(event) {
                var relevaAccessToken = "<?php echo $access_token; ?>";
                var relevaConsentCookieName = '<?php echo $relevaai_request_setting['consent_cookie_name']; ?>';
                var relevaConsentCookieValue = '<?php echo $relevaai_request_setting['consent_cookie_value']; ?>';

                if (relevaAccessToken === '') {
                  console.warn('Releva access token is missing, Please add your access token from within the Releva plugin settings to enable your integration.');
                  return;
                }
                Releva.push(relevaAccessToken,
                    window.relevaPushObject,
                    window.relevaPushCallback,
                    window.relevaErrorCallback,
                    {
                      serviceWorkerUrl: window.relevaServiceWorkerUrl,
                      hasConsent: relevaConsentCookieName ? function() {
                        var hasConsentResult = relevaConsentCookieValue ? Releva.getCookie(relevaConsentCookieName) === relevaConsentCookieValue : !!Releva.getCookie(relevaConsentCookieName)
                        if (!hasConsentResult) {
                          console.warn('Data consent was not given, configuring Releva to operate without collecting data. If you believe this to be an error, check the Releva configuration under section "Privacy and Data Protection"');
                          return false;
                        }
                        return true;
                      } : undefined
                    }
                );
                event.stopImmediatePropagation();
            });
        </script>
        <script nitro-exclude type="text/javascript" onload="document.dispatchEvent(new Event('relevaSdkLoaded'));" src="https://releva.ai/sdk/v0/js/releva-sdk-js.min.js"></script>
        <?php

    }
}

?>
