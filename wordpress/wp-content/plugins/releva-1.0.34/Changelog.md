# 1.0.34
- Added configuration for data protection compliance

# 1.0.33

- Sync product meta data as custom fields proper key - value pair types

# 1.0.32

- Sync product meta data as custom fields

# 1.0.31

- Can set Search page from Releva app

# 1.0.30

- Can set Home and Cart page from Releva app

# 1.0.29

- Add Product Variations Ids in custom data

# 1.0.28

- Get current currency 

# 1.0.27

- Fix showing discount and list prices correctly(when variations of product)

# 1.0.26

- Adding all variant prices into custom data

# 1.0.25

- Fix showing variant price correctly

# 1.0.24

- Fix PHP warnings on PHP 8.1

# 1.0.23

- Consider tax when syncing product prices

# 1.0.22

- Properly track profile info in guest checkout using order->get_billing-\*

# 1.0.21

- Propagate product untrash to Releva

# 1.0.20

- Exclude <PERSON><PERSON><PERSON> from nitropack optimizations

# 1.0.19

- Add wpnonce to window so it can be used in templates

# 1.0.18

- Bugfix: handle missing creation date on product
