# Print Invoice & Delivery Notes for WooCommerce

This is the Print Invoice & Delivery Notes for WooCommerce repository on GitHub. Here you can browse the source, look at open issues and keep track of development.

## Support

This repository is not suitable for support. Please don't use the issue tracker for support requests, but for source code issues only. Support can take place in the [public support forums](http://wordpress.org/support/plugin/woocommerce-delivery-notes) on WordPress.org, where the community can help each other out.

## Contributing

If you have a patch, or stumbled upon an issue with the source code that isn't a [WooCommerce issue](https://github.com/woothemes/woocommerce/issues?labels=Bug&milestone=22&state=open), you can contribute this back to the code. Make sure you have a [GitHub](https://github.com/signup/free) account. Then [submit a ticket](https://github.com/piffpaffpuff/woocommerce-delivery-notes/issues) for your issue and clearly describe the steps to reproduce the bug. 

To correct the issue by your own, you can fork the repository and send a pull request to include your changes.

## Translating

Contribute and update your translations with GitHub. Make sure you have a [GitHub](https://github.com/signup/free) account. Then [fork](https://github.com/piffpaffpuff/woocommerce-delivery-notes/fork) the code, add your translation and send a pull request.

Read the [WordPress Codex](http://codex.wordpress.org/Translating_WordPress) to learn how to translate a plugin.

## Tip

The GitHub App for [Mac](https://mac.github.com) and [Windows](https://windows.github.com) helps managing your forked repository.