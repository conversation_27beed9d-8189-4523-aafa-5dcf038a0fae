	=== Print Invoice & Delivery Notes for WooCommerce ===

Contributors: ashokrane, tychesoftwares
Donate link: https://www.paypal.me/TycheSoftwares
Tags: packing slips, invoice, pdf invoice, delivery notes, woocommerce print invoice
Requires at least: 4.4
Tested up to: 6.8.1
Author URI: https://www.tychesoftwares.com/
Stable tag: 5.7.0
License: GPLv3 or later
License URI: http://www.opensource.org/licenses/gpl-license.php

Create and print PDF invoices, delivery notes and receipts for your WooCommerce orders. Choose your document format from multiple templates.  

== Description ==

> ###🚀&nbsp;&nbsp;New Launch: Flexi BOGO for WooCommerce
>
> The only BOGO plugin with a revenue tracking feature. **Now, at an attractive introductory price**. [Check out our new plugin here](https://www.tychesoftwares.com/products/woocommerce-flexi-bogo-plugin/?utm_source=wprepo&utm_medium=pluginpagetop&utm_campaign=WCDeliveryNotes).

You can print out invoices and delivery notes for the WooCommerce orders. You can also edit the Company/Shop name, Company/Shop postal address and also add personal notes, conditions/policies (like a refund policy) and a footer imprint.

The plugin adds a new side panel on the order page to allow shop administrators to print out the invoice or delivery note. Registered customers can also print their order with a button that is added to the order screen.


= Features =

* Print invoices and delivery notes via the side panel on the "Order Edit" page
* **New!** 2 different layouts for invoice and delivery notes
* **New!** Live preview for templates
* **New!** Attach PDF file to emails based on selected order status
* **New!** Store PDF files in a folder for a specific duration
* **New!** Adjust logo size with custom length and height settings
* Print invoices, delivery notes and receipts via the side panel on the “Order Edit” page
* Quickly print invoices, delivery notes and receipts on the “Orders” page
* Bulk print invoices and delivery notes
* Allow customers to print the order in the “My Account” page
* Include a print link in customer emails
* Add a company address, a logo, and many other details to the invoice and delivery note
* Offers advanced customization support via hooks and filters
* Simple invoice numbering with prefix and suffix options
* Supports sequential order numbers
* Supports the WooCommerce refund system

= Support =

Support can take place in the [public support forums](http://wordpress.org/support/plugin/woocommerce-delivery-notes), where the community can help each other out.

= Contributing =

If you have a patch, or stumbled upon an issue with the source code that isn't a [WooCommerce issue](https://github.com/woothemes/woocommerce/issues?labels=Bug&milestone=22&state=open), you can contribute this back [on GitHub](https://github.com/TycheSoftwares/woocommerce-delivery-notes).

= Translating =

When your language is missing you can contribute a translation to the [GitHub repository](https://github.com/TycheSoftwares/woocommerce-delivery-notes#translating).

**Some of our Pro plugins:**

1. **[Flexi BOGO for WooCommerce](https://www.tychesoftwares.com/products/woocommerce-flexi-bogo-plugin/?utm_source=wprepo&utm_medium=link&utm_campaign=WCDeliveryNotes "Flexi BOGO for WooCommerce")**

1. **[Abandoned Cart Pro for WooCommerce](https://www.tychesoftwares.com/store/premium-plugins/woocommerce-abandoned-cart-pro/?utm_source=wprepo&utm_medium=otherprolink&utm_campaign=WCDeliveryNotes "Abandoned Cart Pro for WooCommerce")**

2. **[Booking & Appointment Plugin for WooCommerce](https://www.tychesoftwares.com/store/premium-plugins/woocommerce-booking-plugin/?utm_source=wprepo&utm_medium=link&utm_campaign=WCDeliveryNotes "Booking & Appointment Plugin for WooCommerce")**

3. **[Order Delivery Date Pro for WooCommerce](https://www.tychesoftwares.com/store/premium-plugins/order-delivery-date-for-woocommerce-pro-21/?utm_source=wprepo&utm_medium=link&utm_campaign=WCDeliveryNotes "Order Delivery Date Pro for WooCommerce")**

4. **[Product Delivery Date Pro for WooCommerce](https://www.tychesoftwares.com/store/premium-plugins/product-delivery-date-pro-for-woocommerce/?utm_source=wprepo&utm_medium=link&utm_campaign=WCDeliveryNotes "Product Delivery Date Pro for WooCommerce")**

5. **[Deposits For WooCommerce](https://www.tychesoftwares.com/store/premium-plugins/deposits-for-woocommerce/?utm_source=wprepo&utm_medium=link&utm_campaign=WCDeliveryNotes "Deposits For WooCommerce")**

6. **[Payment Gateway Based Fees and Discounts for WooCommerce - Pro](https://www.tychesoftwares.com/store/premium-plugins/payment-gateway-based-fees-and-discounts-for-woocommerce-plugin/?utm_source=wprepo&utm_medium=link&utm_campaign=WCDeliveryNotes "Payment Gateway Based Fees and Discounts for WooCommerce - Pro")**

7. **[Custom Order Status for WooCommerce - Pro](https://www.tychesoftwares.com/store/premium-plugins/custom-order-status-woocommerce/?utm_source=wprepo&utm_medium=link&utm_campaign=WCDeliveryNotes "Custom Order Status for WooCommerce - Pro")**

8. **[Custom Order Numbers for WooCommerce - Pro](https://www.tychesoftwares.com/store/premium-plugins/custom-order-numbers-woocommerce/?utm_source=wprepo&utm_medium=link&utm_campaign=WCDeliveryNotes "Custom Order Numbers for WooCommerce - Pro")**

9. **[Product Input Fields for WooCommerce - Pro](https://www.tychesoftwares.com/store/premium-plugins/product-input-fields-for-woocommerce/?utm_source=wprepo&utm_medium=link&utm_campaign=WCDeliveryNotes "Product Input Fields for WooCommerce - Pro")**

10. **[Call for Price for WooCommerce - Pro](https://www.tychesoftwares.com/store/premium-plugins/woocommerce-call-for-price-plugin/?utm_source=wprepo&utm_medium=link&utm_campaign=WCDeliveryNotes "Call for Price for WooCommerce - Pro")**

11. **[Price based on User Role for WooCommerce - Pro](https://www.tychesoftwares.com/store/premium-plugins/price-user-role-woocommerce/?utm_source=wprepo&utm_medium=link&utm_campaign=WCDeliveryNotes "Price based on User Role for WooCommerce - Pro")**

12. **[Currency per Product for WooCommerce - Pro](https://www.tychesoftwares.com/store/premium-plugins/currency-per-product-for-woocommerce/?utm_source=wprepo&utm_medium=link&utm_campaign=WCDeliveryNotes "Currency per Product for WooCommerce - Pro")**

**Some of our other free plugins:**

1. **[Abandoned Cart for WooCommerce](https://wordpress.org/plugins/woocommerce-abandoned-cart/ "Abandoned Cart for WooCommerce")**

2. **[Order Delivery Date for WooCommerce - Lite](https://wordpress.org/plugins/order-delivery-date-for-woocommerce/ "Order Delivery Date for WooCommerce - Lite")**

3. **[Product Delivery Date for WooCommerce - Lite](https://wordpress.org/plugins/product-delivery-date-for-woocommerce-lite/ "Product Delivery Date for WooCommerce")**

4. **[Payment Gateway Based Fees and Discounts for WooCommerce](https://wordpress.org/plugins/checkout-fees-for-woocommerce/ "Payment Gateway Based Fees and Discounts for WooCommerce")**

5. **[Custom Order Status for WooCommerce](https://wordpress.org/plugins/custom-order-statuses-woocommerce/ "Custom Order Status for WooCommerce")**

6. **[Custom Order Numbers for WooCommerce](https://wordpress.org/plugins/custom-order-numbers-for-woocommerce/ "Custom Order Numbers for WooCommerce")**

7. **[Product Input Fields for WooCommerce](https://wordpress.org/plugins/product-input-fields-for-woocommerce/ "Product Input Fields for WooCommerce")**

8. **[Call for Price for WooCommerce](https://wordpress.org/plugins/woocommerce-call-for-price/ "Call for Price for WooCommerce")**

9. **[Price based on User Role for WooCommerce](https://wordpress.org/plugins/price-by-user-role-for-woocommerce/ "Price based on User Role for WooCommerce")**

10. **[Currency per Product for WooCommerce](https://wordpress.org/plugins/currency-per-product-for-woocommerce/ "Currency per Product for WooCommerce")**

**[Developer Documentation](https://www.tychesoftwares.com/docs/print-invoice-apidocs/index.html "Developer Documentation")**

== Installation ==

= Minimum Requirements =

* WooCommerce 2.2 or later
* WordPress 4.4 or later

= Automatic installation =

Automatic installation is the easiest option as WordPress handles the file transfers itself and you don’t need to leave your web browser. To do an automatic install of WooCommerce, log in to your WordPress dashboard, navigate to the Plugins menu and click Add New.

In the search field type “WooCommerce Print Invoice” and click Search Plugins. Once you’ve found the plugin you can view details about it such as the the point release, rating and description. Most importantly of course, you can install it by simply clicking “Install Now”.

= Manual installation =

The manual installation method involves downloading the plugin and uploading it to your webserver via your favourite FTP application. The WordPress codex contains [instructions on how to do this here](http://codex.wordpress.org/Managing_Plugins#Manual_Plugin_Installation).

== Frequently Asked Questions ==

= How to prevent that the Website URL and page numbers are printed? =

You can find an option in the print window of your browser to hide those. This is a browser specific option that can't be controlled by the plugin. Please read the browser help for more information.

= Why are my bulk printed orders not splited to separate pages? =

Your browser is to old to create the page breaks correctly. Try to update it to the latest version or use another browser.

= Even though the shipping and billing address is the same, both are still shown, why? =

It depends on your WooCommerce settings. Addresses are displayed the same way as on the WooCommerce account page. Only one address is printed in case you disabled alternative shipping addresses or the whole shipping. In all other cases both addresses are shown.

= It prints the 404 page instead of the order, how to correct that? =

This is most probably due to the permalink settings. Go either to the WordPress Permalink or the WooCommerce Print Settings and save them again.

If that didn't help, go to the WooCommerce 'Accounts' settings tab and make sure that for 'My Account Page' a page is selected.  

= How do I quickly change the font of the invoice and delivery note? =

You can change the font with CSS. Use the `wcdn_head` hook and then write your own CSS code. It's best to place the code in the `functions.php` file of your theme. 

An example that changes the font and makes the addresses very large. Paste the code in the `functions.php` file of your theme:

`
function example_serif_font_and_large_address() {
	?>
		<style>	
			`#page {
				font-size: 1em;
				font-family: Georgia, serif;
			}
			
			.order-addresses address {
				font-size: 2.5em;
				line-height: 125%;
			}
		</style>
	<?php
`}
add_action( 'wcdn_head', 'example_serif_font_and_large_address', 20 );
`

= Can I hide the prices on the delivery note? =

Sure, the easiest way is to hide them with some CSS that is hooked in with `wcdn_head`.

An example that hides the whole price column and the totals. Paste the code in the `functions.php` file of your theme:

`
function example_price_free_delivery_note() {
	?>
		<style>
			.delivery-note .head-item-price,
			.delivery-note .head-price, 
			.delivery-note .product-item-price,
			.delivery-note .product-price,
			.delivery-note .order-items tfoot {
				display: none;
			}
			.delivery-note .head-name,
			.delivery-note .product-name {
				width: 50%;
			}
			.delivery-note .head-quantity,
			.delivery-note .product-quantity {
				width: 50%;
			}
			.delivery-note .order-items tbody tr:last-child {
				border-bottom: 0.24em solid black;
			}
		</style>
	<?php
}
add_action( 'wcdn_head', 'example_price_free_delivery_note', 20 );
`

= I use the receipt in my POS, can I style it? =

Sure, you can style with CSS, very much the same way as the delivery note or invoice. 

An example that hides the addresses. Paste the code in the `functions.php` file of your theme:

`
function example_address_free_receipt() {
	?>
		<style>
			.content {
				padding: 4% 6%;
			}
			.company-address,
			.order-addresses {
				display: none;
			}
			.order-info li span {
				display: inline-block;
				float: right;
			}
			.order-thanks {
				margin-left: inherit;
			}
		</style>
	<?php
}
add_action( 'wcdn_head', 'example_address_free_receipt', 20 );
`

= Is it possible to remove a field from the order info section? =

Yes, use the `wcdn_order_info_fields` filter hook. It returns all the fields as array. Unset or rearrange the values as you like.

An example that removes the 'Payment Method' field. Paste the code in the `functions.php` file of your theme:

`
function example_removed_payment_method( $fields ) {
	unset( $fields['payment_method'] );
	return $fields;
}
add_filter( 'wcdn_order_info_fields', 'example_removed_payment_method' );
`

=  How can I add some more fields to the order info section? =

Use the `wcdn_order_info_fields` filter hook. It returns all the fields as array. Read the WooCommerce documentation to learn how you get custom checkout and order fields. Tip: To get custom meta field values you will most probably need the `get_post_meta( $order->get_id(), 'your_meta_field_name', true);` function and of course the `your_meta_field_name`. 

An example that adds a 'VAT' and 'Customer Number' field to the end of the list. Paste the code in the `functions.php` file of your theme:

`
function example_custom_order_fields( $fields, $order ) {
	$new_fields = array();
		
	if( get_post_meta( $order->get_id(), 'your_meta_field_name', true ) ) {
		$new_fields['your_meta_field_name'] = array( 
			'label' => 'VAT',
			'value' => get_post_meta( $order->get_id(), 'your_meta_field_name', true )
		);
	}
	
	if( get_post_meta( $order->get_id(), 'your_meta_field_name', true ) ) {
		$new_fields['your_meta_field_name'] = array( 
			'label' => 'Customer Number',
			'value' => get_post_meta( $order->get_id(), 'your_meta_field_name', true )
		);
	}
	
	return array_merge( $fields, $new_fields );
}
add_filter( 'wcdn_order_info_fields', 'example_custom_order_fields', 10, 2 );
`

=  What about the product image, can I add it to the invoice and delivery note? =

Yes, use the `wcdn_order_item_before` action hook. It allows you to add html content before the item name.

An example that adds a 40px large product image. Paste the code in the `functions.php` file of your theme:

`
function example_product_image( $product ) {	
	if( ( '' !== $product->get_id() ) && has_post_thumbnail( $product->get_id() ) ) {
		 echo get_the_post_thumbnail( $product->get_id(), array( 40, 40 ), array( 'loading' => false ) );
	}
}
add_action( 'wcdn_order_item_before', 'example_product_image' );
`

= How can I differentiate between invoice and delivery note through CSS? =

The `body` tag contains a class that specifies the template type. The class can be `invoice` or `delivery-note`. You can prefix your style rules to only target one template. For example you could rise the font size for the addresses on the right side:

`
.invoice .billing-address {
	font-size: 2em;
}

.delivery-note .shipping-address {
	font-size: 2em;
}
`

= How do I customize the look of the invoice and delivery note? =

You can use the techniques from the questions above. Or you consider the `wcdn_head` hook to enqueue your own stylesheet. Or for full control, copy the file `style.css` from `woocommerce-delivery-notes/templates/print-order` to `yourtheme/woocommerce/print-order` and start editing it. 

Note: Create the `woocommerce` and `print-order` folders if they do not exist. This way your changes won't be overridden on plugin updates.

= I would like to move the logo to the bottom, put the products between the shipping and billing address and rotate it by 90 degrees, how can I do that? =

Well, first try it with CSS and some filter/action hooks, maybe the questions above can help you. If this isn't enough, you are free to edit the HTML and CSS of the template. Consider this solution only, if you really know some HTML, CSS and PHP! Most probably you want to edit the `print-content.php` and `style.css`. Copy the files from `woocommerce-delivery-notes/templates/print-order` to `yourtheme/woocommerce/print-order` and start editing them. 

Note: Create the `woocommerce` and `print-order` folders if they do not exists. This way your changes won't be overridden on plugin updates.

= Is there a list of all action and filter hooks? =

Unfortunately there isn't yet. But you can look directly at the template files to see what is available. 

= Which template functions are available? =

You can use the functions from WordPress, WooCommerce and every installed plugin or activated theme. You can find all plugin specific functions in the `wcdn-template-functions.php` file. In addition the `$order`variable in the template is just a normal `WC_Order` instance. 

= Can I download the order as PDF instead of printing it out? =

No, this isn't possible. However, you can store the PDF and attach it to your email.

= I need some more content on the order, how can I add it? =

The plugin uses the exact same content as WooCommerce. If the content isn't available in WooCommerce, then it will neither be in the delivery note and invoice. In case you have some special needs, you first have to enhance WooCommerce to solve your issue. Afterwards you can integrate the solution into the invoice and delivery note template via hooks.

= How can I translate the plugin? =

Upload your language file to `/wp-content/languages/plugins/` (create this folder if it doesn't exist). WordPress will then load the language. Make sure you use the same locale as in your configuration and the correct plugin locale i.e. `woocommerce-delivery-notes-it_IT.mo/.po`. 

Please [contribute your translation](https://github.com/TycheSoftwares/woocommerce-delivery-notes#translating) to include it in the distribution.

== Screenshots ==

1. The clean invoice print view.
2. Live preview for Invoice, Receipt, and Delivery notes.
3. Attach PDF file to emails based on selected order status.
4. Quick print buttons on the order edit page.
5. Customers can also print the order.
6. Quick print actions.

== External Services ==
This plugin communicates with our tracking server to send usage data **only** if the user has explicitly opted in to usage tracking. For detailed information about what is tracked, please refer to our [usage tracking documentation](https://www.tychesoftwares.com/docs/woocommerce-print-invoice-delivery-note/print-invoice-usage-trackings/).

== Changelog ==

= 5.7.0 - 13/06/2025 =
* Fix - Issue where variation item meta (such as size and color) was not appearing on invoices due to changes in the latest WooCommerce update v9.9.0.

= 5.6.0 - 27/05/2025 =
* Fix - Invoice numbers were not generated in ascending order based on the order ID.
* Fix - Added wcdn_invoice_order_total_label filter to allow "Total" and "Subtotal" labels in the default invoice template.
* Fix - Compatibility issue with the Sales By State plugin, causing a critical error when printing the invoice template.
* Fix - Function name conflict by adding a wcdn prefix to all plugin functions, preventing issues like PHP Fatal error: Cannot redeclare function get_product_name().
* Tweak - Updated for compatibility with WordPress 6.8.1.
* Tweak - Updated for compatibility with WooCommerce 9.8.5.

= 5.5.0 - (04.03.2025) =
* Fix- Issue where bulk printing was being initiated multiple times. Added a popup for bulk action selection and a print button to open the print window in a new tab.
* Fix- Added the 'allow_user_email_order_access' filter to allow non-logged-in users to access the Print link in emails.
* Fix- Fixed broken logo issue in the Simple template of the plugin.
* Fix- Security vulnerability by preventing direct access to PDF files and adding unique keys to PDF filenames.
* Fix- PHP fatal error: "Uncaught Error: Call to a member function get_meta() on bool" when the invoice number was missing on the edit order page.

= 5.4.1 - (18.12.2024) =
* Fix- Vulnerability in the 'wcdn_remove_shoplogo' AJAX action by adding proper capability checks. This ensures that only authorized users can modify the shop logo.

= 5.4.0 - (05.12.2024) =
* Fix- Resolved an issue preventing guest users from viewing the invoice via the Print button on the order received page and the print link in the order email.
* Fix- Added a notice for logged-out users to log in when attempting to access invoices via a direct link or Print link in the order email.

= 5.3.0 - (21.11.2024) =
* Fix - Anyone could view customer invoices by simply visiting the URL in the format sent in the email.
* Fix - PHP Notice: Trying to access array offset on value of type bool.
* Fix - A slash was added before single quotes in the Shop address field after saving setting.
* Tweak - Started loading the external resources(js, css, images) from the plugin itself instead of external URLs.
* Tweak - Removed the call to our tracking server to fetch the deactivation reasons and added them in the plugin itself.

= 5.2.0 - (22.10.2024) =
* Enhancement - Added the 'wcdn_show_print_button_for_order_status' filter to control the visibility of the print button on the My Account page based on order status.
* Fix - Deactivation survey request was being sent even though 'Skip & deactivate' was selected while deactivating the plugin.
* Fix - Invoice, receipt, and delivery notes buttons not appearing on the order edit page.
* Fix - Refunded products not being removed from the invoice, receipt, and delivery notes.
* Fix - FAQ page incorrectly appearing in the Dashboard menu.
* Fix - Resolved admin.js loading error on order edit and listing pages.
* Fix - Conflict with the Conditional Checkout Fields for WooCommerce plugin, where it was not allowing to add or edit conditional checkout fields.
* Fix - Strings that have not been translation ready.

= 5.1.0 (02.07.2024) =
* Fix- Fixed an error in the debug log when the "Numbering" option is disabled.
* Fix- Fixed a critical error on the invoice with the Woo Donations plugin to ensure compatibility.
* Fix- Fixed the PDF font size for the default template.
* Fix- Added new hook 'wcdn_head_pdf' to apply css to PDF.
* Fix- Fixed the issue where printing orders in bulk did not separate individual orders onto separate pages.
* Fix- Added a meta name of robots content noindex in the templates.

= 5.0.2 (12.06.2024) =
* Fix - HTML tags are not functioning properly on the invoice.
* Fix - Fatal error while placing the order, due to conflic with PDF Invoices & Packing Slips for WooCommerce plugin.

= 5.0.1 (07.06.2024) =
* Fix - Logo was getting printed twice after v5.0 update.
* Fix - Bulk Action Print Invoice issue.

= 5.0.0 (05.06.2024) =
This release contains a major update for the plugin, focusing primarily on the all-new backend user interface, along with several bug fixes.

Major Updates:

* Changed the UI of plugin settings page.
* Added a setting for template type, and now customize your template with different settings.
* Added a template setting page to customize the template.
* Added live preview for customization of the template.
* Added size setting for the company logo.
* Added setting To store PDF files for a specific duration.
* Added email attachment with template PDF files.
* Added Invoice menu in the WooCommerce menu.

Bug fixes :

* Fix - Bulk printing being initiated multiple times
* Fix - Translations for the print link in emails do not work

= 4.9.0 (27.02.2024) =
* Fix - Cross-site request forgery vulnerability in the plugin.
* Fix - Added a filter called 'wcdn_print_text_in_email' & 'wcdn_print_view_in_browser_text_in_email' for changing the "Print: Open print view in browser" text in email URL.
* Fix - Fatal Error on the edit order page.
* Fix - Additional product metadata is printed in the invoice.
* Fix - Added a filter called 'wcdn_address_billing' to the shipping address Title.
* Fix - Incorrect order date in the invoice.

= 4.8.1 (10.10.2023) =
* Fix - Same invoice number & invoice date was displayed on all invoices and the counter was not being increased.
* Fix - Error Notice was displayed on the settings page with PHP 8.2.

= 4.8.0 (04.10.2023) =
* Enhancement - Compatibility With WooCommerce High Performance Order Storage (HPOS)
* Fix - Fatal error was displayed in the Invoice with WooCommerce Product Add-ons plugin.

= 4.7.3 (21.03.2023) =
* Fix - Cross-Site Request Forgery vulnerability in the plugin.

= 4.7.2 (02.02.2023) =
* Fix - Fixed a Reflected XSS vulnerability in the plugin.
* Fix - Fixed a fatal error which occurs when plugins apart from ours are customizing the Bulk Actions on the WooCommerce Orders page.

= 4.7.1 (07.12.2022) =
* Fix :- Fix :- Removed the labels for shipping and billing address. This was added in 4.7.0, which is now reverted back.

= 4.7.0 (06.12.2022) =
* Fix :- Logo was not showing on Android phone earlier. This is fixed now.
* Fix :- Display labels for shipping and billing address
* Fix :- Removed redundant "Refund" line. 
* Fix :- Compatibility with WooCommerce Product Addons plugin. All the fields from the plugin will be shown in the Invoice, Receipt & Delivery Note.

= 4.6.5 (08.02.2022) =
* Enhancement :- Added an option to print the invoice text in Right to Left direction.
* Enhancement :- Added a filter called 'wcdn_product_meta_data' to remove the meta fields of the product in the invoice.
* Fix :- Quantity column was showing wrong total in the print invoice on the frontend when the invoice contains WooCommerce Composite products.
* Fix :- Extra tabs were added in the bulk action print link.
* Fix :- When using the file field in the WooCommerce Product Addons plugin whole path was getting printed instead of the filename.
* Fix :- The invoice number was not appearing when we open the invoice from the Order email sent to the customer.
* Fix :- Debug log errors are been fixed.

= 4.6.4 (21.07.2021) =
* Fix :- After updating to v4.6.3 , fatal error was coming in some sites where the invoice template has been customized by copying print-content.php file in the theme folder. This is fixed now.

= 4.6.3 (19.07.2021) =
* Fix :- Fixed the errors coming with PHP 8.
* Fix :- Shipping address was not printed in invoice when the order is created manually. This is fixed now.
* Fix :- When printing receipts, the number of downloaded files is displayed as "%s files". This is fixed now. Props to @inc2734 for the PR.
* Dev :- Translated the plugin in Korean language. Props to @shga89 for the PR.

= 4.6.2 (11.12.2020) =

Enhancement :- Added an option to insert the print link in the admin emails.
Fix :- Strings of Bulk Printing options were not getting translated. This is fixed now. Props to @pomegranate
Fix :- Finnish language locale name was incorrect. This is fixed now.
Fix :- Custom fields on WooCommerce product page from Product Input Fields for WooCommerce plugin were not coming in the invoice. This is fixed now.
Fix :- The BULK printing options of WooCommerce DYMO Print (PRO version) stopped working after installing our Print invoices plugin. This has been fixed. Props to @pepbc
Tweak :- In FAQ page changed the code snippet to add the products image in the invoice.

= 4.6.1 (23.10.2020) =

* Fix :- Tracker cron event was not running properly which is fixed now.
* Dev :- With the help of filters now able to change the name of the print invoice and delivery notes in the dropdown menu on Orders page.

= 4.6.0 (22.09.2020) =

* Fix :- Notice was coming since WooCommerce V4.4.0 which is fixed now.
* Fix :- Bulk printing function was using old hooks & filters.Have changed them with the new ones.
* Dev :- Now the Total number of quantity of the products in the row of quantity in invoice will be shown.
* Dev :- With the help of filter now one can hide the child products in Composite Products.
* Dev :- Translated the plugin in Greek language. Props to @edeuter for the PR.

= Minimum Requirements: WooCommerce 3.0 =

= 4.5.5 (12.03.2020) =

* Changed the plugin name to 'Print Invoice & Delivery Notes for WooCommerce'

= 4.5.4 (11.03.2020) =

* Compatibility with WooCommerce v4.0.0

= 4.5.3 (26.09.2019) =

* The plugin now allows 3rd party code to add order item data on the print-content.php template. Props to @doozy for the PR.

= 4.5.2 (23.08.2019) =

* Fixed the issue of Print buttons not working on sites with PHP versions below 7.0.

= 4.5.1 (21.08.2019) =

* Fixed the issue of Print buttons not showing up on Order details page after the 4.4.9 update.

= 4.5.0 (21.08.2019) =

* Added a missing file from v4.4.9.

= 4.4.9 (21.08.2019) =

* Made the plugin code compliant with WPCS coding standards
* Added filter wcdn_theme_print_button_template_type_arbitrary - this filter hook allows to change template type based on order status
* Added filters wcdn_print_button_name_on_my_account_page, wcdn_print_button_name_order_page - these filter hooks allows to change the label of the Print button
* When plugin is uninstalled, data cleanup wasn't happening. This has been fixed.

= 4.4.8 (02.04.2019) =

* Fix - When a noticed was dismissed from the plugin, then it will dismiss all other notices from other plugins also. This is fixed now. 
* Fix - Some errors in debug.log file are fixed. 

= 4.4.7 (24.11.2018) =

* Fix – Fixed compatibility related issue with WooCommerce Product Add-ons v3.0.x. Options were not being printed.
* Fix – Fixed compatibility related issue with Woocommerce Partial Orders Pro Plugin.

= 4.4.6 (22.10.2018) =

* .po, .mo is updated for all the other languages.

= 4.4.5 (22.10.2018) =

* .pot file updated.

= 4.4.4 (13.10.2018) =

* Removed the Welcome page of the plugin and also removed the promotional notices which were being displayed on admin end of the WordPress website.

= 4.4.3 (23.07.2018) =

* Usage Tracking has been added in the plugin. It provides an option to allow  tracking of the non-sensitive data of our plugin from the website. You can read more about it [here](https://www.tychesoftwares.com/docs/docs/woocommerce-print-invoice-delivery-note/usage-tracking/).

= 4.4.2 (22.02.2018) =

* Fix - With the latest WooCommerce version 3.3, the icons for Print were missing. This is fixed.

= 4.4.1 (29.12.2017) =

* This version has 1 bug fix.

* Fix - PHP short tag was inadvertently added in the plugin in v4.4, which was causing an error. This has been fixed.

= 4.4 (29.12.2017) =

* This version has 1 bug fix.

* Fix - Earlier with WooCommerce Local Pickup Plus v2.x.x, pickup locations were not displayed on the invoices, delivery notes & receipts. Now, the plugin is compatible with it and it will display the pickup locations. 

* Code Enhancement - Now, the plugin has the uninstall file for deleting the plugin options.

= 4.3.6 (19.12.2017) =

* Added translation for the word 'Price' for dutch language
* Removed Pro version link that was not going anywhere

= 4.3.5 (14.11.2017) =

* Fix issue of invoice date label not appearing translated

= ******* (07.11.2017) =

* Updating missing .mo files for Japanese and Norwegian.

= 4.3.4 (26.09.2017) =

* .po and .mo files for Japanese and Norwegian language is added in the plugin. The plugin strings can now be translated to this languages using these files.

= 4.3.3 (13.09.2017) =

* Fix - The unwanted attributes from products were getting displayed in the invoice.

* Fix - Notice of deprecated function get_item_downloads() in the invoice for downloadable products.

= 4.3.2 (05.09.2017) =

* In this version deprecated functions and classes have been removed. Because of that attributes were missing and attribute slug was getting printed on Invoice page. This has been fixed.

* Fix - There was no line break before SKU element for variable products on Invoice page. This has been fixed.

* Fix - The deprecated function WC_Order::get_order_currency has been removed and replaced with get_currency().

= 4.3.1 (23.08.2017) =

* This version has 1 bug fix.

* Fix - The attributes of variable product were not displayed with the variation name in the Print screen. This has been fixed.

= 4.3 (19.08.2017) =

* This version has 1 bug fix.

* Fix - Warnings were displayed on My Account, Checkout, Orders page with WooCommerce version 3.0.x. This has been fixed.

= 4.2.0 =

* Tweak - Refactored settings screen
* Fix - Compatibility with latest WooCommerce
* Fix - Print preview loading indicators
* Fix - Icon font embed
* Dev - Load only one instance of the plugin (singleton class)
* Dev - New settings hooks that work better with WooCommerce

= 4.1.6 =

* Fix - More flexible protocol checks of email permalinks

= 4.1.5 =

* Fix - Check protocol of email permalinks
* Fix - Show preview links on the settings page
* Fix - Consistent privileges for users with admin access

= 4.1.4 =

* Fix - Double check the bulk action type to improve bulk order deletion
* Fix - Default arguments for the E-Mail hook

= 4.1.3 =

* Feature - Better support for WooCommerce 2.3 refunds

= 4.1.2 =

* Fix - For a fatal error caused by an unknown property in the plugin update system
* Fix - Where the 'Types' options didn't stick in some cases

= 4.1.1 =

* Fix - For a fatal error caused by an unknown property in the plugin update system

= 4.1 =

* Feature - Support for WooCommerce 2.2 refunds 
* Feature - Option to add a print link to customer emails
* Tweak - Code improvements and some new hooks

= 4.0.2 =

* Tweak - Second attempt for better spacing between price columns

= 4.0.1 =

* Tweak - Better spacing between price columns

= 4.0 =

* The print page now also includes the item price
* Customize titles with your own translation file (woocommerce-delivery-notes-xx_XX.mo). Place it in /wp-content/languages/plugins to override the plugin language. 

= 3.4.2 =

* Remove some left over developer comments from the print page

= 3.4.1 =

* Fix an issue where a blank page was printed in WooCommerce 2.1

= 3.4 =

**Note: The template was modified. Please check your print-content.php if you copied it to your theme directory.**

* Improved WooCommerce 2.2 compatibility
* Fix an issue were shipping and billing addresses did not respect the WooCommerce settings
* Better way to reset the invoice number counter

= 3.3.1 =

* Small code improvements

= 3.3 =

* WordPress 4.0 and WooCommerce 2.2 compatibility
* Fix an issue where the print buttons were hidden after the update

= 3.2.3 =

* WordPress 4.0 and WooCommerce 2.2 compatibility
* Template: Improved the CSS to generate less blank pages
* Fixed the settings link on the Plugins page

= 3.2.2 =

* Some language upadates. Old outdated languages were removed. Please [contribute your language](https://github.com/piffpaffpuff/woocommerce-delivery-notes#translating)!
* Fix a print issue with not completed orders.
* Tweaked JavaScript for the theme print button to gracefully handle non-modern browsers.

= 3.2.1 =

* New template function for the invoice date 
* Fix invoice number display logic 
* Fix slashes in the options fields

= 3.2 =

* Improved theme print button: Print the invoice only for completed orders and a receipt for all other order states. This can be changed via 'wcdn_theme_print_button_template_type' filter hook.
* Fix the print button on the "Thank You" page for guest checkouts
* Added CSS classes to the admin side panel

= 3.1.1 =

* Fix the hidden loading indicator on order edit screen
* Other small visual optimizations
* Later plugin load hook for better compatibility

= 3.1 =

**Note: Template changes had to be made. Please control your template after the update in case you applied some custom styling.**

* By popular demand the 'Quantity' column is back in the template
* Basic invoice numbering

= 3.0.6 =

* Fixed the known issue where the print button stopped working becuse of SSL
* Fixed an issue where the print page was redirected to the account page 

= 3.0.5 =

**Known issue: Printing won't work when your account uses SSL and the rest of the page doesn't. The issue will be fixed in a future version.**

* Added SKU to the template
* Modified the alignment of product attributes in the template
* Print buttons in the theme will print the invoice (can be changed with hook) 

= 3.0.4 =

* Save the endpoint at activation to not print a 404 page. (Note: Try to resave the print settings if the issue persists after the update.)

= 3.0.3 =

**Attention: This update works only with WooCommerce 2.1 (or later) and Wordpress 3.8 (or later). Install it only if your system meets the requirements.**

* Supports only WooCommerce 2.1 (or later)
* Bulk print actions
* Print buttons in the front-end
* Redesigned template look
* New template structure and action hooks

== Upgrade Notice ==

= 4.2.0 =

4.2.0 requires at least WooCommerce 2.2.

= 4.1.5 =

4.1.5 requires at least WooCommerce 2.2.
