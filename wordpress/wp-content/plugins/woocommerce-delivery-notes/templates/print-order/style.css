/* Simple CSS Reset and Print options
------------------------------------------*/


html, body, div, span, h1, h2, h3, h4, h5, h6, p, a, table, ol, ul, dl, li, dt, dd {
	border: 0 none;
	font: inherit;
	margin: 0;
	padding: 0;
	vertical-align: baseline;
}

body {
	line-height: 1;
}

ol,
ul {
	list-style: none;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}


/* Template Page Layout
------------------------------------------*/


/* Main Body */
body {
	background: #fff;
	color: #000;
	font-family: "HelveticaNeue", Helvetica, Arial, sans-serif;
	font-size: 100%;
	line-height: 1.25em;
}

h1,
h2,
h3,
h4 {
	font-weight: bold;
	margin-bottom: 1.25em;
}

ul {
	margin-bottom: 0.1em;
}

li,
dt,
dd  {
	padding: 0.1em 0;
}

dt {
	font-weight: bold;
}

p + p {
	margin-top: 1.25em;
}

address {
	font-style: normal;
}

/* Basic Table Styling */
table {
	page-break-inside: auto;
	width: 100%;
}

tr {
	page-break-inside: avoid;
	page-break-after: auto;	
	border-bottom: 0.12em solid #bbb;
}

td,
th {
	padding: 0.375em 0.75em 0.375em 0;
	vertical-align: top;
}

td img,
th img {
	vertical-align: top;
}

th {
	color: black;
	font-weight: bold;
	text-align: left;
	padding-bottom: 1.25em;
}

tfoot {
	display: table-row-group;
}

/* Page Margins & Basic Stylings */
#page {
	margin-left: auto;
	margin-right: auto;
	text-align: left;
	font-size: 0.875em;
}

.content {
	padding-left: 10%;
	padding-right: 10%;
	padding-top: 5%;
	padding-bottom: 5%;
}

.content + .content {
	page-break-before: always;
}

h1,
h2 {
	font-size: 1.572em;
}

.order-branding,
.order-addresses,
.order-info,
.order-items,
.order-notes,
.order-thanks,
.order-colophon {
	margin-bottom: 3em;
}

.order-items {
	page-break-before: auto;
	page-break-after: auto;
}

/* Order Branding */
.order-branding .company-logo {
	margin-bottom: 1em;
}

/* Order Addresses */
.order-addresses {
	margin-bottom: 6em;
}

.order-addresses:after {
	content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.billing-address {
	width: 50%;
	float: left;
}

.shipping-address {
	width: 50%;
	float: left;
}

/* Switch the addresses for invoices */

/* Order Info */
.order-info ul {
	border-top: 0.24em solid black;
}

.order-info li {
	border-bottom: 0.12em solid #bbb;
}

.order-info li strong {
	min-width: 18%;
	font-weight: normal;
	display: inline-block;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	margin-bottom: 0;
	padding-right: 0.35em;
}

.order-info li span {
    display: inline-block;
	overflow: hidden;
}

/* Order Items */
.order-items {
	margin-bottom: 1em;
}

.order-items .head-name,
.order-items .product-name,
.order-items .total-name {
	width: 50%;
}

.order-items .head-quantity,
.order-items .product-quantity,
.order-items .total-quantity,
.order-items .head-item-price,
.order-items .product-item-price,
.order-items .total-item-price {
	width: 15%;
}

.order-items .head-price,
.order-items .product-price,
.order-items .total-price {
	width: 20%;
}

.order-items p {
	display: inline;
}

.order-items small,
.order-items dt,
.order-items dd {
	font-size: 0.785em;
	font-weight: normal;
	line-height: 150%;
	padding: 0;
	margin: 0;
}

.order-items dt,
.order-items dd {
	display: block;
	float: left;
}

.order-items dt {
	clear: left;
	padding-right: 0.2em;
}

.order-items .product-name .attachment {
	display: block;
	float: left; 
	margin-right: 0.5em;
	width: 36px;
}

.order-items .product-name .attachment img {
	max-width: 100%;
	height: auto;
}

.order-items .product-name .name,
.order-items .product-name .extras {
	overflow: hidden;
}

.order-items tfoot tr:first-child,
.order-items tfoot tr:last-child {
	font-weight: bold;
}

.order-items tfoot tr:last-child .total-price .amount:first-child {
	font-weight: bold;
}

.order-items tfoot tr:last-child {
	border-bottom: 0.24em solid black;
}

/* Order Notes */
.order-notes {
	margin-top: 3em;
	margin-bottom: 6em;
}

.order-notes h4 {
	margin-bottom: 0;
}

/* Order Thanks */
.order-thanks {
	margin-left: 50%;
}

/* Order Colophon */
.order-colophon {
	font-size: 0.785em;
	line-height: 150%;
	margin-bottom: 0;
}

.colophon-policies {
	margin-bottom: 1.25em;
}


/* CSS Media Queries for Print
------------------------------------------*/


@media print {
	body {
		font-size: 8pt;
	}
	
	.content {
		/* Remove padding to not generate empty follow up pages */
		padding-bottom: 0;
	}
}