/* Pdf file style CSS */
html, body, div, span, h1, h2, h3, h4, h5, h6, p, a, table, ol, ul, dl, li, dt, dd {
	border: 0 none;
	margin: 0;
	padding: 0;
	vertical-align: middle;
}
ol, ul, li {
	list-style: none;
}
body {
	background: #fff;
	font-family: "HelveticaNeue", Helvetica, Arial, sans-serif;
	padding: 5% 10%;
}
h1, h2, h3, h4 {
	margin-bottom: 10px;
}
.cap {
	text-transform: uppercase;
}

/* Page Margins & Basic Stylings */
.content {
	margin-left: auto;
	margin-right: auto;
}
.page-header {
	margin-bottom: 2em;
	padding-bottom: 2%; 
	border-bottom: 0.24em solid black;
	width: 100%;
}
.company-logo {
	display: inline-block;
	width: 49%;
}
.document-name {
	display: inline-block;
	width: 50%;
	text-align: right;
}
.order-info li {
	border-bottom: 2px solid #bbb;
	width: 100%;
	padding: 10px 0px 5px 0px;
}
.order-info li strong {
	min-width: 30%;
	display: inline-block;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
	width: 100%;
}
tr {
	page-break-inside: avoid;
	page-break-after: auto;	 
	border-bottom: 0.12em solid #bbb;
}
td, th {
	padding: 0.375em 0.75em 0.375em 0;
	vertical-align: middle;
}
td img, th img {
	vertical-align: middle;
}
th {
	font-weight: bold;
	text-align: left;
	padding-bottom: 1.25em;
}
tfoot {
	display: table-row-group;
}
.order-items .head-name,
.order-items .product-name,
.order-items .total-name {
	width: 50%;
}

.order-items .head-quantity,
.order-items .product-quantity,
.order-items .total-quantity,
.order-items .head-item-price,
.order-items .product-item-price,
.order-items .total-item-price {
	width: 15%;
}

.order-items .head-price,
.order-items .product-price,
.order-items .total-price {
	width: 20%;
}

.order-items p {
	display: inline;
}

.order-items small,
.order-items dt,
.order-items dd {
	font-size: 0.785em;
	font-weight: normal;
	padding: 0;
	margin: 0;
}
.order-items .product-name .attachment {
	display: block;
	float: left; 
	margin-right: 0.5em;
	width: 36px;
}
.order-items .product-name .attachment img {
	max-width: 100%;
	height: auto;
}
.order-items tfoot tr:first-child,
.order-items tfoot tr:last-child {
	font-weight: bold;
}
.order-items tfoot tr:last-child .total-price .amount:first-child {
	font-weight: bold;
}

.order-addresses:after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}
.billing-address {
	width: 50%;
	float: left;
}
.shipping-address {
	width: 50%;
	float: left;
}

.order-stamp-container {
	position: absolute;
	top: calc( 100vh / 2 );
	left: 50%;
	font-size: 75px;
	opacity: 0.2;
	transform: translate( -50%, -50% ) rotate(45deg);
	font-family: 'Times New Roman', Times, serif;
}

.order-notes,
.order-thanks,
.order-colophon,
.order-info,
.order-items,
.order-addresses {
	margin-top: 2em;
	margin-bottom: 2em;
}

.order-items {
	border-bottom: 0.24em solid black;
}
.order-notes {
	text-align: justify;
}
.order-colophon {
	text-align: center;
	padding-top: 2%;
	width: 100%;
}
.order-thanks {
	width: 100%;
	vertical-align: top;
}
.personal_note {
	display: inline-block;
	width: 49%;
	vertical-align: top;
}
.colophon-policies {
	display: inline-block;
	width: 50%;
	text-align: right;
	vertical-align: top;
}
/* CSS Media Queries for Print
------------------------------------------*/

@media print {
	
	.content {
		/* Remove padding to not generate empty follow up pages */
		padding-bottom: 0;
		page-break-after: always;
	}

	.content:last-child {
        page-break-after: auto; /* Prevents an extra blank page at the end */
    }
}