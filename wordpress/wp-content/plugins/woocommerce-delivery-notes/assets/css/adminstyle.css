/* live preview template file style CSS */
html, body, div, span, h1, h2, h3, h4, h5, h6, p, a, table, ol, ul, dl, li, dt, dd {
	border: 0 none;
	margin: 0;
	padding: 0;
}
ol, ul, li {
	list-style: none;
}
body {
	background: #fff;
}
h1, h2, h3, h4 {
	margin-bottom: 5px;
}
.cap {
	text-transform: uppercase;
}

/* Page Margins & Basic Stylings */
.content {
	margin-left: auto;
	margin-right: auto;
}
.page-header {
	margin-bottom: 2em;
	padding-bottom: 2%; 
	border-bottom: 0.24em solid black;
	width: 100%;
}
.company-logo {
	display: inline-block;
	width: 47%;
}
.document-name {
	display: inline-block;
	width: 50%;
	text-align: right;
}
.order-info li {
	border-bottom: 2px solid #bbb;
	width: 100%;
	padding: 5px 0px 5px 0px;
}
.order-info li span {
	min-width: 40%;
	display: inline-block;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
	width: 100%;
	font-size: 15px;
}
tr {
	page-break-inside: avoid;
	page-break-after: auto;	 
	border-bottom: 0.12em solid #bbb;	
}
td, th {
	vertical-align: middle;
}
td img, th img {
	vertical-align: middle;
}
th {
	font-weight: bold;
	text-align: left;
	padding-bottom: 5px;
}
tfoot {
	display: table-row-group;
}
.order-items .head-name,
.order-items .product-name,
.order-items .total-name {
	width: 50%;
}

.order-items .head-quantity,
.order-items .product-quantity,
.order-items .total-quantity,
.order-items .head-item-price,
.order-items .product-item-price,
.order-items .total-item-price {
	width: 15%;
}

.order-items .head-price,
.order-items .product-price,
.order-items .total-price {
	width: 20%;
}

.order-items p {
	display: inline;
}

.order-items small,
.order-items dt,
.order-items dd {
	font-size: 0.785em;
	font-weight: normal;
	padding: 0;
	margin: 0;
}
.order-items .product-name .attachment {
	display: block;
	float: left; 
	margin-right: 0.5em;
	width: 36px;
}
.order-items .product-name .attachment img {
	max-width: 100%;
	height: auto;
}
.order-items tfoot tr:first-child,
.order-items tfoot tr:last-child {
	font-weight: bold;
}
.order-items tfoot tr:last-child .total-price .amount:first-child {
	font-weight: bold;
}

.order-addresses:after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}
.billing-address {
	width: 50%;
	float: left;
}
.shipping-address {
	width: 50%;
	float: left;
}
.order-notes,
.order-thanks,
.order-colophon,
.order-items,
.order-addresses {
	margin-top: 2em;
	margin-bottom: 1em;
}
.order-info {
	margin-top: 10PX;
	margin-bottom: 1em;
}
.info-list {
	padding-left: 0rem;
}
.order-items {
	border-bottom: 0.24em solid black;
}
.order-notes {
	text-align: justify;
}
.order-colophon {
	text-align: center;
	padding-top: 2%;
	width: 100%;
}
.order-thanks {
	width: 100%;
	vertical-align: top;
}
.personal_note {
	display: inline-block;
	width: 47%;
	vertical-align: top;
}
.colophon-policies {
	display: inline-block;
	width: 50%;
	text-align: right;
	vertical-align: top;
}
.order-stamp-container {
    position: absolute;
    top: 60%;
    left: 70%;
	font-size: 75px;
	opacity: 0.2;
    transform: translate(-50%, -50%) rotate(45deg);
	font-family: 'Times New Roman', Times, serif;
}
.wcdn_preview_template{
	font-family: "HelveticaNeue", Helvetica, Arial, sans-serif;
}
.company-name{
	font-weight: bold;
}
address {
    font-style: italic;
}
.cap {
font-size: 1em;
font-weight: bold;
}
.notices {
    border: 2px solid red;
    padding: 10px;
    background-color: #f2e7e7;
	font-size: 10px;
}

.order-infoo ul {
	border-top: 0.24em solid black;
}

.order-infoo li {
	border-bottom: 0.12em solid #bbb;
}
.order-infoo li strong {
	min-width: 30%;
	font-weight: normal;
	display: inline-block;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	margin-bottom: 0;
	padding-right: 0.35em;
}
.order-infoo li span {
    display: inline-block;
	overflow: hidden;
}
.colophon-policiess {
    margin-bottom: 1.25em;
}
.order-colophonn {
    font-size: 0.785em;
    line-height: 150%;
    margin-bottom: 0;
}
.logo .mobile {
    display: none;
}
.order-branding .company-logo {
    margin-bottom: 1em;
}
.billing-addresss{
	width: 50%;
	float: left;
}
.billing-addresss address,
.shipping-addresss address {
    font-style: normal;
}
.billing-addresss h3,
.shipping-addresss h3 {
	font-weight: bold;
	margin-bottom: 1em;
	font-size: 1em;
}
.order-infoo h2 {
	font-size: 1.4em;
	font-weight: bold;
	margin-bottom: 1.25em;
}
.order-notess h4{
	font-size: 15px;
	font-weight: bold;
	margin-top: 3em;
}
.order-thankss {
	font-size: 0.900em;
	margin-top: 3em;
    margin-bottom: 3em;
}
.order-notess h5 {
	font-size: 15px;
}
.footer-content * {
    font-size: inherit;
    color: inherit;
}