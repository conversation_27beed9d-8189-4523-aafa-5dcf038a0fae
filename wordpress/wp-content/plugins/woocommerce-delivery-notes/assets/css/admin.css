/* Icons */
@font-face {
	font-family: 'icons';
	src: url('../fonts/icons.eot');
	src: url('../fonts/icons.eot?#iefix') format('embedded-opentype'),
	   url('../fonts/icons.woff') format('woff'),
	   url('../fonts/icons.ttf') format('truetype'),
	   url('../fonts/icons.svg#icons') format('svg');
	font-weight: normal;
	font-style: normal;
}
.icon-flex {
	position: relative;
}
.icon-flex .dashicons {
    position: absolute;
    left: -12px;
    top: 5px;
    font-size: 16px;
}
.col-sm-6.icon-flex label + label {
	padding-left: 10px;
}
.wcdn_container .nav-tabs {
    background-color: #575757;
    padding: 10px 0px 0px 0px;
    border-bottom: 0px;
    display: flex;
    align-items: center;
    position: relative;
}
ul.wcdn_main_tab li:last-child {
    right: 20px;
    position: absolute;
    color: #ffffff;
}
.wcdn_container .nav-tabs.non-bg {
	background: transparent;
	padding-top: 0;
}
.wcdn_container .nav-tabs .nav-link {
	border: 0;
    color: #ffffff;
    outline: 0;
    font-weight: 600;
    font-size: 15px;
    padding: 15px;
    margin-bottom: 0px;
    text-transform: capitalize;
}
.wcdn_container .nav-tabs.non-bg .nav-link { 
	color: #000000;
}
.wcdn_container .logo_size_container {
	display: flex;
	align-items: center;
}
.wcdn_container .logo_size_container span {
	margin: 0px 10px;
}
.wcdn_container .nav-tabs .nav-link:focus, .wcdn_container .nav-tabs .nav-link:hover {
    border: 0;
    border-radius: 0;
    outline: none;
    box-shadow: none;
}
.wcdn_container .nav-tabs .nav-item.show .nav-link, .wcdn_container .nav-tabs .nav-link.active {
	border: 0;
	border-radius: 0;
	outline: none;
    box-shadow: none;
}
.wcdn_container .nav-tabs.non-bg  .nav-item.show .nav-link, .wcdn_container .nav-tabs.non-bg .nav-link.active {
	border: 0;
    border-bottom: 3px solid #575758;
}
.wcdn_container .nav-tabs.non-bg .nav-link:focus, .wcdn_container .nav-tabs.non-bg .nav-link:hover {
    color: #000000;
}
.wcdn_container .nav-link:focus, .wcdn_container .nav-link:hover {
    color: #ffffff;
}
.wcdn_container .nav-item {
	margin-bottom: 0px;
}
.wcdn_container .wcdn_title {
	border-bottom: 1px dashed #ccc;
	font-size: 15px;
    font-weight: bold;
    padding-bottom: 5px;
    margin: 20px 0px;
}
.wcdn_container .tab_container {
    margin: 20px 0px;
}
.wcdn_container .form-group.row {
    margin-bottom: 20px;
}
.wcdn_container .col-form-label {
    font-size: 14px;
    font-weight: 600;
	padding-top: 0px;
}
.wcdn_container input.form-control {
	border: 1px solid #ced4da;
}
.wcdn_container .card-body {
    padding: 20px 15px;
}

.wcdn_container .card-body  {
    color: #000000;
    font-size: 20px;
    font-weight: 600;
    outline: 0;
}
.wcdn_container .accordion-icon {
    border-style: solid;
	margin-right: 7px;
    border-width: 6px 6px 6px 6px;
    border-color: transparent transparent transparent rgb(11, 11, 11);
    transition: transform 0.3s ease;
}
.wcdn_container .accordion-item.expanded .accordion-icon {
    transform: rotate(90deg);
}

.wcdn_container .wcdn_heading {
	font-size: 20px;
	margin: 0;
	margin-right: 10px;
    line-height: 1.3;
}
.wcdn_container select {
	width: 100%;
}
.wcdn_container .accordion .accordion-item {
	margin-bottom: 20px;
}
.wcdn_container .accordion .accordion-item:first-of-type {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
}
.wcdn_container .accordion .accordion-item:not(:first-of-type) {
	border: 1px solid rgba(0,0,0,.125);
}
.wcdn_container .accordion .accordion-header {
    margin-bottom: 0;
    display: flex;
    padding: 15px;
    color: #0c63e4;
    background-color: #e7f1ff;
}
.wcdn_container .accordion .accordion-button {
    padding: 0px;
    color: #000;
    background-color: #e7f1ff;
    border: 0;
    border-radius: 0;
    box-shadow: none;
}
.wcdn_container .accordion .accordion-button::after {
	background-image: inherit;
}
.wcdn_container .accordion .accordion-button:not(.collapsed)::after {
	background-image: inherit;
}
.wcdn_container .switch {
    position: relative;
    display: inline-block;
	padding-left: 10px;
    width: 44px;
    height: 25px;
}
.col-sm-6 .label{
	padding-left: 10px;
}
.wcdn_container .accordion .switch {
    position: relative;
    display: inline-block;
    width: 54px;
    height: 25px;
}
/* Hide default HTML checkbox */
.wcdn_container .switch input {
	opacity: 0;
	width: 0;
	height: 0;
}
/* The slider */
.wcdn_container .slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	-webkit-transition: .4s;
	transition: .4s;
}
.wcdn_container .slider:before {
	position: absolute;
	content: "";
	height: 15px;
	width: 15px;
	left: 4px;
	bottom: 5px;
	background-color: white;
	-webkit-transition: .4s;
	transition: .4s;
}
.wcdn_container input:checked + .slider {
  	background-color: #2196F3;
}
.wcdn_container input:focus + .slider {
  	box-shadow: 0 0 1px #2196F3;
}
.wcdn_container input:checked + .slider:before {
	-webkit-transform: translateX(21px);
	-ms-transform: translateX(21px);
	transform: translateX(21px);
}
.wcdn_container #wcdn_faq .accordion-item, .wcdn_container #wcdn_filters .accordion-item {
	margin-bottom: 0px;
}
.wcdn_container #wcdn_faq .accordion-header, .wcdn_container #wcdn_filters .accordion-header {
    color: #ffffff;
    background-color: #575757;
    font-weight: 400;
}
.wcdn_container #wcdn_faq .accordion-button, .wcdn_container #wcdn_filters .accordion-button {
    color: #ffffff;
    background-color: #575757;
}
.wcdn_container .accordion-flush .accordion-collapse {
    border-width: 0;
    padding: 15px;
}
.wcdn_container .accordion-flush#wcdn_faq .accordion-collapse, .wcdn_container .accordion-flush#wcdn_filters .accordion-collapse {
    border-width: 0;
    padding: 15px;
}
/* Rounded sliders */
.wcdn_container .slider.round {
  	border-radius: 34px;
}
.wcdn_container .slider.round:before {
  	border-radius: 50%;
}
.wcdn_container .card img {
	height: 30px;
    width: 30px;
    margin-right: 6px;
}

.image-upload-wrap {
	margin-top: 20px;
    border: 1px dashed #C7CBD1;
    position: relative;
    text-align: center;
    padding: 30px;
}
.image-upload-wrap input {
	position: absolute;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    outline: none;
    opacity: 0;
    cursor: pointer;
}
.file-upload-content {
  	display: none;
  	text-align: center;
  	border: 1px dashed #C7CBD1;
    position: relative;
    text-align: center;
    padding: 30px;
}
span.remove-image {
	position: absolute;
    top: -8px;
    right: -12px;
    cursor: pointer;
}
.file-upload-content span.remove-image img {
    height: 35px;
    width: 35px;
}
.file-upload-image {
	height: 150px;
    width: 150px;
}


/* Edit order */
#woocommerce-delivery-notes-box .inside {
	margin: 0;
	padding: 0;
}
#woocommerce-delivery-notes-box .button {
	overflow: hidden;
	max-width: 100%;
	text-overflow: ellipsis;
	margin-top: 0.65em;
	margin-bottom: 0.35em;
}
#woocommerce-delivery-notes-box span.print-preview-loading {
	position: absolute;
	right: 2.5em;
	top: -2.1em;
}
#woocommerce-delivery-notes-box .print-actions {
	padding: 10px 12px;
}
#woocommerce-delivery-notes-box .print-info {
	margin-top: 0.3em;
	border-top: 1px solid #ddd;
	padding: 10px 12px;
	margin-bottom: 0;
	background-color: #f5f5f5;
}
/* All orders */
.type-shop_order .column-order_actions .print-preview-button,
.type-shop_order .column-wc_actions .print-preview-button {
	display: block;
	text-indent: -9999px;
	position: relative;
	height: 1em;
	width: 1em;
	padding: 0 !important;
	height: 2em !important;
	width: 2em;
}
.type-shop_order .column-order_actions .print-preview-button:before,
.type-shop_order .column-wc_actions .print-preview-button:before {
	font-family: "icons";
	speak: none;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	-webkit-font-smoothing: antialiased;
	margin: 0;
	text-indent: 0;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	text-align: center;
	content: "";
	line-height: 1.85;
	content: "\e603";
} 
.type-shop_order .column-order_actions .print-preview-button.invoice:before,
.type-shop_order .column-wc_actions .print-preview-button.invoice:before {
	content: "\e602";
}
.type-shop_order .column-order_actions .print-preview-button.delivery-note:before,
.type-shop_order .column-wc_actions .print-preview-button.delivery-note:before {
	content: "\e601";
}
.type-shop_order .column-order_actions .print-preview-button.receipt:before,
.type-shop_order .column-wc_actions .print-preview-button.receipt:before {
	content: "\e604";
}
.type-shop_order .column-order_actions .print-preview-button.credit-note:before,
.type-shop_order .column-wc_actions .print-preview-button.credit-note:before {
	content: "\e600";
}
.type-shop_order .column-order_actions span.print-preview-loading,
.type-shop_order .column-wc_actions span.print-preview-loading {
	float: left;
	margin-top: 3px;
}
.type-shop_order .column-order_actions .print-preview-button span,
.type-shop_order .column-wc_actions .print-preview-button span {
	display:  none;
}
.print-preview-loading {
	visibility: hidden;
	vertical-align: middle;
	margin: 0;
}
.print-preview-loading.is-active {
	visibility: visible;
}
/* Settings */
.wcdn-image-select-field {
	position: relative;
	overflow: hidden;
	background-color: white;
	border: 1px solid #ddd;
	width: 125px;
	height: 125px;
}
.wcdn-image-select-attachment {
	position: relative;
	width: 100%;
	height: 100%;
	cursor: pointer;
}
.wcdn-image-select-attachment .thumbnail {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	-webkit-transform: translate( 50%, 50% );
	-ms-transform: translate(50%,50%);
	transform: translate( 50%, 50% );
}
.wcdn-image-select-attachment img {
	position: absolute;
	top: 0;
	left: 0;
	max-height: 100%;
	max-width: none;
	-webkit-transform: translate( -50%, -50% );
	-ms-transform: translate(-50%,-50%);
	transform: translate( -50%, -50% );
}
.wcdn-image-select-attachment img.portrait {
	max-height: none;
	max-width: 100%;
}
.wcdn-image-select-spinner {
	float: none;
	position: absolute;
	top: 0;
	left: 0;
	margin: 10px;
} 
.wcdn-image-select-buttons {
	position: absolute;
	left: 0;
	bottom: 0;
	padding-left: 10px;
	padding-bottom: 10px;
}
.wcdn-image-select-buttons .button {
	max-width: 105px;
	white-space: nowrap;
	overflow: hidden;
	-webkit-text-overflow: ellipsis;
	-ms-text-overflow: ellipsis;
	text-overflow: ellipsis;
}
.wcdn-footer .alert.alert-dark.alert-dismissible.fade.show {
    background: #F2F2F2;
    border-color: #DCDCDE;
    margin-bottom: 30px;
	font-size: 13px;
}
.wcdn-footer .alert.alert-dark.alert-dismissible.fade.show a {
	border-bottom: 1px solid #424242;
	color: #424242;
	text-decoration: none;
}
.wcdn-footer button.close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 12px;
    color: inherit;
    border: none;
    text-align: right;
	font-size: 20px;
}
.wcdn-footer .alert .msg-icon {
    vertical-align: middle;
    margin-right: 15px;
}
.wcdn-footer-top {
	display: none;
}
.wcdn_preview_img img {
	width: 100%;
	border: 2px solid #000;
}
.wcdn_preview_img h6 {
	font-size: 14px;
    text-align: center;
    margin-top: 10px;
}
.wcdn-footer { padding: 30px 0; }
.wcdn-footer .ft-text { text-align: center; }
.wcdn-footer .ft-text a { border-bottom: 1px solid #41278D; text-decoration: none;}
.wcdn-footer .ft-text a:hover, .wcdn-footer .ft-text a:active, .wcdn-footer .ft-text a:focus { border-color: #136826; }
.wcdn-footer .ft-text strong { font-weight: 600; }
.wcdn-footer .ft-text p { line-height: 25px; margin-bottom: 22px; }
.wcdn-footer .ft-text p:last-child { margin-bottom: 0; }
.wcdn-footer .ft-text .rating { font-size: 17px; color: #FFBA00; }
.custom-label {
	font-size: 14px;
    font-weight: 600;
	max-width: 12% !important;
}