<?php

/**
 * pinkangel functions and definitions
 *
 * @package pinkangel
 */

if ( ! function_exists( 'pinkangel_setup' ) ) :
	function pinkangel_setup() {
		load_theme_textdomain( 'pinkangel', get_template_directory() . '/languages' );
		add_theme_support( 'automatic-feed-links' );
		add_theme_support( 'title-tag' );
		add_theme_support( 'post-thumbnails' );
		set_post_thumbnail_size( 200, 200, true );
		add_image_size( 'pinkangel-featured', 1200, 1200, true );
		add_image_size( 'pinkangel-gallery', 600, 600, true );
		add_image_size( 'pinkangel-listing', 400, 400, true );
		add_image_size( 'pinkangel-100', 100, 100, true );
		add_image_size( 'pinkangel-70', 70, 70, true );
		add_image_size( 'pinkangel-30', 30, 30, true );
        register_nav_menus( array(
            'primaryL' => __( 'Primary Left', 'pinkangel' ),
            'primaryR' => __( 'Primary Right', 'pinkangel' ),
            'headerL' => __( 'Header Left', 'pinkangel' ),
            'headerR' => __( 'Header Right', 'pinkangel' ),
            'footerL'  => __( 'Footer Section', 'pinkangel' ),
            'footerRT'  => __( 'Footer Information', 'pinkangel' ),
            'footerS'  => __( 'Footer Social', 'pinkangel' ),
		) );
		add_theme_support( 'html5', array(
			'comment-list', 'comment-form', 'search-form', 'gallery', 'caption',
		) );
	}
endif;

add_action( 'after_setup_theme', 'pinkangel_setup' );

add_filter('woocommerce_defer_transactional_emails', '__return_true' );

function theme_scripts(){

    global $wp_query;

    wp_register_style('style-main', get_template_directory_uri() . '/style.css', array(), '1.2.3', 'all');
    wp_enqueue_style('style-main');

    wp_register_script('script-main', get_template_directory_uri() . '/owl-carousel.js', array('jquery'), '1.2.0', true);
    wp_enqueue_script('script-main');

    wp_register_script('script-owl-carousel', get_template_directory_uri() . '/main.js', array('jquery'), '1.2.0', true);
    wp_enqueue_script('script-owl-carousel');

}

add_action( 'wp_enqueue_scripts', 'theme_scripts' );



/* DISABLE WORDPRESS FUNCS */

add_filter('xmlrpc_enabled', '__return_false');
remove_filter( 'authenticate', 'wp_authenticate_email_password', 20 );
remove_action('welcome_panel', 'wp_welcome_panel');
function wpb_imagelink_setup() {
    $image_set = get_option( 'image_default_link_type' );
     
    if ($image_set !== 'none') {
        update_option('image_default_link_type', 'none');
    }
}
add_action('admin_init', 'wpb_imagelink_setup', 10);

/*  DISABLE GUTENBERG STYLE IN HEADER| WordPress 5.9 */
function wps_deregister_styles() {
    wp_dequeue_style( 'global-styles' );
}
add_action( 'wp_enqueue_scripts', 'wps_deregister_styles', 100 );

//Remove Gutenberg Block Library CSS from loading on the frontend
function smartwp_remove_wp_block_library_css(){
    wp_dequeue_style( 'wp-block-library' );
    wp_dequeue_style( 'wp-block-library-theme' );
    wp_dequeue_style( 'wc-blocks-style' ); // Remove WooCommerce block CSS
} 
add_action( 'wp_enqueue_scripts', 'smartwp_remove_wp_block_library_css', 100 );

/**
 * Disable the emoji's
 */
function disable_emojis() {
 remove_action( 'wp_head', 'print_emoji_detection_script', 7 );
 remove_action( 'admin_print_scripts', 'print_emoji_detection_script' );
 remove_action( 'wp_print_styles', 'print_emoji_styles' );
 remove_action( 'admin_print_styles', 'print_emoji_styles' ); 
 remove_filter( 'the_content_feed', 'wp_staticize_emoji' );
 remove_filter( 'comment_text_rss', 'wp_staticize_emoji' ); 
 remove_filter( 'wp_mail', 'wp_staticize_emoji_for_email' );
 add_filter( 'tiny_mce_plugins', 'disable_emojis_tinymce' );
 add_filter( 'wp_resource_hints', 'disable_emojis_remove_dns_prefetch', 10, 2 );
}
add_action( 'init', 'disable_emojis' );

/**
 * Filter function used to remove the tinymce emoji plugin.
 * 
 * @param array $plugins 
 * @return array Difference betwen the two arrays
 */
function disable_emojis_tinymce( $plugins ) {
 if ( is_array( $plugins ) ) {
 return array_diff( $plugins, array( 'wpemoji' ) );
 } else {
 return array();
 }
}

/**
 * Remove emoji CDN hostname from DNS prefetching hints.
 *
 * @param array $urls URLs to print for resource hints.
 * @param string $relation_type The relation type the URLs are printed for.
 * @return array Difference betwen the two arrays.
 */
function disable_emojis_remove_dns_prefetch( $urls, $relation_type ) {
 if ( 'dns-prefetch' == $relation_type ) {
 /** This filter is documented in wp-includes/formatting.php */
 $emoji_svg_url = apply_filters( 'emoji_svg_url', 'https://s.w.org/images/core/emoji/2/svg/' );

$urls = array_diff( $urls, array( $emoji_svg_url ) );
 }

return $urls;
}

/* DISABLE /feed/ */

function wpb_disable_feed() {
  wp_die( __('No feed available from Pink Angel, please visit our Dashboard!') );
}

add_action('do_feed', 'wpb_disable_feed', 1);
add_action('do_feed_rdf', 'wpb_disable_feed', 1);
add_action('do_feed_rss', 'wpb_disable_feed', 1);
add_action('do_feed_rss2', 'wpb_disable_feed', 1);
add_action('do_feed_atom', 'wpb_disable_feed', 1);
add_action('do_feed_rss2_comments', 'wpb_disable_feed', 1);
add_action('do_feed_atom_comments', 'wpb_disable_feed', 1);

remove_action( 'wp_head', 'feed_links_extra', 3 );
remove_action( 'wp_head', 'feed_links', 2 );

add_filter( 'wpseo_next_rel_link', '__return_false' );
add_filter( 'wpseo_prev_rel_link', '__return_false' );

add_filter( 'woocommerce_product_tabs', 'woo_new_tab1' );
add_filter( 'woocommerce_product_tabs', 'woo_new_tab2' );
add_filter( 'woocommerce_product_tabs', 'woo_new_tab3' );
add_filter( 'woocommerce_product_tabs', 'woo_new_tab4' );
add_filter( 'woocommerce_product_tabs', 'woo_new_tab5' );

function woo_new_tab1( $tabs ) {	
  $tabs['table'] = array(
    'title' 	=> __( 'Таблица за размери', 'pinkangel' ),
    'priority' 	=> 20,
    'callback' 	=> 'woo_new_tab_table'
  );
  return $tabs;
}
function woo_new_tab2( $tabs ) {	
  $tabs['phone'] = array(
    'title' 	=> __( 'Телефон', 'pinkangel' ),
    'priority' 	=> 30,
    'callback' 	=> 'woo_new_tab_phone'
  );
  return $tabs;
}
function woo_new_tab3( $tabs ) {	
  $tabs['delivery'] = array(
    'title' 	=> __( 'Кога ще получа поръчката? 2-3 работни дни', 'pinkangel' ),
    'priority' 	=> 40,
    'callback' 	=> 'woo_new_tab_delivery'
  );
  return $tabs;
}
function woo_new_tab4( $tabs ) {	
  $tabs['return'] = array(
    'title' 	=> __( 'Замяна и връщане', 'pinkangel' ),
    'priority' 	=> 50,
    'callback' 	=> 'woo_new_tab_return'
  );
  return $tabs;
}

function woo_new_tab5( $tabs ) {	
  $tabs['contact'] = array(
    'title' 	=> __( 'Връзка с нас', 'pinkangel' ),
    'priority' 	=> 60,
    'callback' 	=> 'woo_new_tab_contact'
  );
  return $tabs;
}

function woo_new_tab_table() {
  if (get_field('table') != '') echo "<img src='".get_field('table')."' alt='".__( 'Таблица за размери', 'pinkangel' )."'>";
  else echo __( 'Няма въведена таблица за размери', 'pinkangel' );
}

function woo_new_tab_phone() {
  if (get_field('phone',2139) != ''){
    $phone = get_field('phone',2139);
    $phone = str_replace(' ', '', $phone);
    $phone = substr($phone, 1);
    $phone = "+359".$phone;
  ?>
    <a href="tel:<?php echo $phone; ?>"><strong><span class="fa-solid fa-phone"></span> <?php echo __( 'Телефон', 'pinkangel' ); ?>: <?php echo get_field('phone',2139); ?></strong></a>
  <?php }
}

function woo_new_tab_delivery() {
  if (get_field('delivery',2139) != '') echo get_field('delivery',2139);
}

function woo_new_tab_return() {
  if (get_field('return',2139) != '') echo get_field('return',2139);
}

function woo_new_tab_contact() {
  if (get_field('contact',2139) != '') echo get_field('contact',2139);
}

function woocommerce_template_single_title() {
   echo '<h5 class="' . esc_attr( apply_filters( 'woocommerce_product_loop_title_classes', 'woocommerce-loop-product__title' ) ) . '">' . get_the_title() . '</h5>'; 
}

add_action( 'woocommerce_before_single_product_summary', 'woocommerce_template_single_title', 25 );

remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_meta', 40 );
add_action( 'woocommerce_after_single_product_summary', 'woocommerce_template_single_meta', 12 );

add_filter( 'woocommerce_sale_flash', 'add_percentage_to_sale_badge', 20, 3 );
function add_percentage_to_sale_badge( $html, $post, $product ) {

  if( $product->is_type('variable')){
      $percentages = array();

      // Get all variation prices
      $prices = $product->get_variation_prices();

      // Loop through variation prices
      foreach( $prices['price'] as $key => $price ){
          // Only on sale variations
          if( $prices['regular_price'][$key] !== $price ){
              // Calculate and set in the array the percentage for each variation on sale
              $percentages[] = round( 100 - ( floatval($prices['sale_price'][$key]) / floatval($prices['regular_price'][$key]) * 100 ) );
          }
      }

      // Skip adding the percentage if there are no variations on sale (plugins may set a sale price without a value in 'sale_price')
      if ( empty($percentages) ) return;
      // We keep the highest value
      $percentage = max($percentages) . '%';

  } elseif( $product->is_type('grouped') ){
      $percentages = array();

      // Get all variation prices
      $children_ids = $product->get_children();

      // Loop through variation prices
      foreach( $children_ids as $child_id ){
          $child_product = wc_get_product($child_id);

          $regular_price = (float) $child_product->get_regular_price();
          $sale_price    = (float) $child_product->get_sale_price();

          if ( $sale_price != 0 || ! empty($sale_price) ) {
              // Calculate and set in the array the percentage for each child on sale
              $percentages[] = round(100 - ($sale_price / $regular_price * 100));
          }
      }
      // We keep the highest value
      $percentage = max($percentages) . '%';

  } else {
      $regular_price = (float) $product->get_regular_price();
      $sale_price    = (float) $product->get_sale_price();

      if ( $sale_price != 0 || ! empty($sale_price) ) {
          $percentage    = round(100 - ($sale_price / $regular_price * 100)) . '%';
      } else {
          return $html;
      }
  }
  return '<span class="onsale">-' . $percentage . '</span>';
}

add_filter( 'woocommerce_add_to_cart_fragments', 'wc_refresh_mini_cart_count');
function wc_refresh_mini_cart_count($fragments){
    ob_start();
    $items_count = WC()->cart->get_cart_contents_count();
    ?>
    <div id="mini-cart-count" class="cart-items"><?php echo $items_count; ?></div>
    <?php
        $fragments['#mini-cart-count'] = ob_get_clean();
    return $fragments;
}

add_filter( 'woocommerce_add_to_cart_fragments', 'header_add_to_cart_fragment', 30, 1 );
function header_add_to_cart_fragment( $fragments ) {
    global $woocommerce;
    ob_start();
    ?>
    <div id="pa_miniCart">
      <?php echo woocommerce_mini_cart(); ?>
    </div>
    <?php
    $fragments['#pa_miniCart'] = ob_get_clean();

    return $fragments;
}

function currentYear(){
    return date('Y');
}

add_filter( 'woocommerce_default_catalog_orderby', 'pinkangel_default_catalog_orderby' );

function pinkangel_default_catalog_orderby( $sort_by ) {
	return 'date';
}

/**
 * Change number of products that are displayed per page (shop page)
 */
add_filter( 'loop_shop_per_page', 'new_loop_shop_per_page', 20 );

function new_loop_shop_per_page( $cols ) {
  // $cols contains the current number of products per page based on the value stored on Options –> Reading
  // Return the number of products you wanna show per page.
  $cols = 24;
  return $cols;
}

add_filter( 'woocommerce_get_price_html', 'ecommercehints_display_sale_price_dates', 100, 2 );
function ecommercehints_display_sale_price_dates( $price, $product ) {
    $sales_price_from = get_post_meta( $product->id, '_sale_price_dates_from', true );
    $sales_price_to   = get_post_meta( $product->id, '_sale_price_dates_to', true );
    if ( is_product() && $product->is_on_sale() && $sales_price_to != "" ) {
        $sales_price_date_from = date( "l jS F", $sales_price_from );
        $sales_price_date_to   = date( "l jS F", $sales_price_to );
        $price .= "<br><small>Sale from " . $sales_price_date_from . ' to ' . $sales_price_date_to . "</small>";
	}
    return $price;
}

// Fix coupon functionality on checkout page
function pinkangel_fix_checkout_coupon_scripts() {
    if (is_checkout()) {
        // Ensure WooCommerce checkout scripts are loaded
        wp_enqueue_script('wc-checkout');

        // Add comprehensive coupon fix script
        $inline_script = "
        jQuery(document).ready(function($) {
            console.log('PinkAngel: Initializing coupon fix');

            // Remove any conflicting handlers
            $(document).off('submit', 'form.checkout_coupon');
            $(document).off('click', '.woocommerce-remove-coupon');

            // Fix coupon form submission
            $(document).on('submit', 'form.checkout_coupon', function(e) {
                e.preventDefault();
                var form = $(this);
                var coupon_code = form.find('input[name=\"coupon_code\"]').val().trim();

                console.log('PinkAngel: Coupon form submitted with code:', coupon_code);

                if (!coupon_code) {
                    console.log('PinkAngel: Empty coupon code');
                    return false;
                }

                // Show loading state
                form.find('button').prop('disabled', true).text('Прилагане...');

                // Use WooCommerce AJAX
                $.ajax({
                    type: 'POST',
                    url: wc_checkout_params.ajax_url,
                    data: {
                        action: 'woocommerce_apply_coupon',
                        security: wc_checkout_params.apply_coupon_nonce,
                        coupon_code: coupon_code
                    },
                    success: function(response) {
                        console.log('PinkAngel: Coupon AJAX response:', response);

                        // Reset button
                        form.find('button').prop('disabled', false).text('Прилагане');

                        if (response.success) {
                            // Clear the input
                            form.find('input[name=\"coupon_code\"]').val('');
                            // Trigger checkout update
                            $('body').trigger('update_checkout');
                        } else {
                            // Show error
                            $('.woocommerce-error, .woocommerce-message').remove();
                            form.before('<div class=\"woocommerce-error\">' + (response.data || 'Грешка при прилагане на купона') + '</div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log('PinkAngel: Coupon AJAX error:', error);
                        form.find('button').prop('disabled', false).text('Прилагане');
                        $('.woocommerce-error, .woocommerce-message').remove();
                        form.before('<div class=\"woocommerce-error\">Грешка при прилагане на купона. Моля опитайте отново.</div>');
                    }
                });

                return false;
            });

            // Fix coupon removal
            $(document).on('click', '.woocommerce-remove-coupon', function(e) {
                e.preventDefault();
                var link = $(this);
                var coupon = link.attr('data-coupon');

                console.log('PinkAngel: Removing coupon:', coupon);

                $.ajax({
                    type: 'POST',
                    url: wc_checkout_params.ajax_url,
                    data: {
                        action: 'woocommerce_remove_coupon',
                        security: wc_checkout_params.remove_coupon_nonce,
                        coupon: coupon
                    },
                    success: function(response) {
                        console.log('PinkAngel: Remove coupon response:', response);
                        $('body').trigger('update_checkout');
                    }
                });

                return false;
            });
        });
        ";
        wp_add_inline_script('wc-checkout', $inline_script);
    }
}
add_action('wp_enqueue_scripts', 'pinkangel_fix_checkout_coupon_scripts', 999);

// Debug coupon application issues
function pinkangel_debug_coupon_application() {
    if (is_checkout() && WP_DEBUG) {
        error_log('Checkout page loaded - debugging coupon functionality');

        // Check if coupons are enabled
        $coupons_enabled = get_option('woocommerce_enable_coupons');
        error_log('Coupons enabled: ' . ($coupons_enabled ? 'yes' : 'no'));

        // Check for any hooks that might interfere
        global $wp_filter;
        if (isset($wp_filter['woocommerce_apply_coupon'])) {
            error_log('Hooks on woocommerce_apply_coupon: ' . count($wp_filter['woocommerce_apply_coupon']));
        }
    }
}
add_action('wp', 'pinkangel_debug_coupon_application');

// Ensure coupon AJAX works properly
function pinkangel_ensure_coupon_ajax() {
    // Make sure WooCommerce AJAX is properly initialized
    if (!wp_doing_ajax()) {
        return;
    }

    if (isset($_POST['action']) && $_POST['action'] === 'woocommerce_apply_coupon') {
        error_log('Coupon AJAX request received: ' . print_r($_POST, true));
    }
}
add_action('init', 'pinkangel_ensure_coupon_ajax', 1);

// Ensure coupon functionality is not disabled by other plugins
function pinkangel_ensure_coupon_functionality() {
    // Force enable coupons if they're disabled
    if (get_option('woocommerce_enable_coupons') !== 'yes') {
        update_option('woocommerce_enable_coupons', 'yes');
    }

    // Ensure checkout coupon display is enabled
    if (get_option('woocommerce_enable_checkout_coupon') !== 'yes') {
        update_option('woocommerce_enable_checkout_coupon', 'yes');
    }
}
add_action('init', 'pinkangel_ensure_coupon_functionality');

// Fix any conflicts with discount rules plugins
function pinkangel_fix_discount_rules_conflicts() {
    // Remove any hooks that might interfere with standard coupon functionality
    if (class_exists('Wdr\App\Controllers\DiscountCalculator')) {
        // Ensure standard WooCommerce coupon hooks are preserved
        add_action('woocommerce_applied_coupon', function($coupon_code) {
            error_log('PinkAngel: Coupon applied successfully: ' . $coupon_code);
        });

        add_action('woocommerce_removed_coupon', function($coupon_code) {
            error_log('PinkAngel: Coupon removed successfully: ' . $coupon_code);
        });
    }
}
add_action('plugins_loaded', 'pinkangel_fix_discount_rules_conflicts', 999);
