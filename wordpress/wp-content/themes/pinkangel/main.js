function scrollTop() {
  var scrollTop = jQuery(window).scrollTop();
  if (scrollTop > 0) {
    jQuery('#header').addClass('stickyHeader');
  } else {
    jQuery('#header').removeClass('stickyHeader');
  }
}

function resizeWindow() {
  if (window.innerWidth <= 1024) {

    jQuery('.headMenu > div > ul li.menu-item-has-children > a').click(function () {
      jQuery(this).parent('li').addClass('openedMenu');
      var href = jQuery(this).attr('href');
      var title = jQuery(this).text();
      jQuery('.submenuTopper').remove();
      jQuery(this).next().prepend(`
        <li class="submenuTopper">
          <div class="submenuTopperWrap">
            <span class="submenuTopperBack"><span class="fa-light fa-chevron-left"></span></span>
            <span class="submenuTopperTitle">` + title + `</span>
            <span class="submenuTopperAll"><a href="` + href + `">виж всички</a></span>
          </div>
        </li>
      `);
      menuBack();
      return false;
    });

    jQuery('li.current-product_cat-ancestor.current-menu-ancestor > a').click();

  } else {

    jQuery('li.menu-item').removeClass('openedMenu');
    jQuery('.submenuTopper').remove();
    jQuery('.headMenu > div > ul li.menu-item-has-children > a').unbind();
    jQuery('.submenuTopperBack').unbind();
  
  }
}

function menuBack() {

  jQuery('.submenuTopperBack').click(function () {
    var parenthref = jQuery(this).parents('ul.sub-menu').eq(1).prev().attr('href');
    var parenttitle = jQuery(this).parents('ul.sub-menu').eq(1).prev().text();
    jQuery(this).parents('li.menu-item').eq(0).removeClass('openedMenu');
    if (parenttitle != undefined && parenttitle != '') {
      jQuery(this).parents('li.menu-item').eq(0).parent().prepend(`
        <li class="submenuTopper">
          <div class="submenuTopperWrap">
            <span class="submenuTopperBack"><span class="fa-light fa-chevron-left"></span></span>
            <span class="submenuTopperTitle">` + parenttitle + `</span>
            <span class="submenuTopperAll"><a href="` + parenthref + `">виж всички</a></span>
          </div>
        </li>
      `);
    } else {
      jQuery('.submenuTopper').remove();
    }

    jQuery(this).parents('.submenuTopper').remove();
    menuBack();
  });
}

function rightPanelClose(e) {
  var target = jQuery(e.target);
  if (target.parents('.rightPanel').length == 0 && target.parents('.headAccountItem').length == 0) {
    jQuery('.rightPanel').removeClass('rightPanelOpened');
  }
}

jQuery(document).ready(function ($) {

  // const list = ['localhost'];
  // if (list.includes(window.location.hostname)) {
  //     window.fbq = function() {};
  // }

  $('.menu-menu_2022-container ul li a, #crumb a[href*="/product-category/"]').each(function () {
    var thisHref = $(this).attr('href');
    thisHref = thisHref + '?orderby=date&wpf_count=24&wpf_fbv=1&all_products_filtering=1';
    $(this).attr('href', thisHref);
  });

  if ($(window).width() > 1024) {
    $('#accordion-container .description_tab').click();
  }

  $(document).bind('click', rightPanelClose);

  scrollTop();
  resizeWindow();

  $(window).scroll(function () {
    scrollTop();
  });

  $(window).resize(function () {
    resizeWindow();
  });

  let store_notice_closed = localStorage['store-notice-closed'];
  if (store_notice_closed) {
    $('.woocommerce-store-notice').remove();
  }

  $('.woocommerce-store-notice').insertAfter('#header .headSite');
  $('.woocommerce-store-notice .woocommerce-store-notice__dismiss-link').html('<i aria-hidden="true" class="fa-light fa-close"></i>');
  $('.woocommerce-store-notice').click(function () {
    $('.woocommerce-store-notice').remove();
    localStorage['store-notice-closed'] = "yes";
  });

  $('.headMobile').click(function () {
    $('#header, html').toggleClass('openedMenu');
  });

  $('.pa_productFiltersMobile').click(function () {
    $('.pa_productFilters').addClass('pa_productFiltersOpened');
    $('html').addClass('openedMenu');
  });

  $('.pa_productFiltersCategories').click(function () {
    $('.pa_catWrap').addClass('pa_catprodWrapOpenedMenu');
    $('html').addClass('openedMenu');
  });

  $('.pa_productFiltersMobileClose, .pa_productFilters .wpfFilterContent .wpfCheckboxHier ul li > label, .pa_productFilters .wpfFilterButtons .wpfFilterButton.wpfButton, .pa_productFilters .wpfFilterButtons .wpfClearButton.wpfButton').click(function () {
    $('.pa_productFilters').removeClass('pa_productFiltersOpened');
    $('html').removeClass('openedMenu');
  });

  $('.pa_productFiltersCategoriesClose').click(function () {
    $('.pa_catWrap').removeClass('pa_catprodWrapOpenedMenu');
    $('html').removeClass('openedMenu');
  });

  $('.headAccountItemProfile span').click(function () {
    $('.rightPanel').removeClass('rightPanelOpened');
    $('#panelProfile').addClass('rightPanelOpened');
  });

  $('.headAccountItemCart span').click(function () {
    $('.rightPanel').removeClass('rightPanelOpened');
    $('#panelCart').addClass('rightPanelOpened');
  });

  $('.headAccountItemFavorites span').click(function () {
    $('.rightPanel').removeClass('rightPanelOpened');
    $('#panelFavorites').addClass('rightPanelOpened');
  });

  $('.headAccountItemSearch span').click(function () {
    $('.rightPanel').removeClass('rightPanelOpened');
    $('#panelSearch').addClass('rightPanelOpened');
  });

  $('.rightPanel .rightPanelTopClose span').click(function () {
    $(this).parents('.rightPanel').removeClass('rightPanelOpened');
  });

  // Handle cart updates without forcing page reload for coupon functionality
  $(document).on('click', '.remove_from_cart_button', function (e) {
    // Only reload for mini-cart removals, not checkout page operations
    if (!$('body').hasClass('woocommerce-checkout')) {
      setTimeout(function () {
        window.location.reload();
      }, 1000);
    }
  });

  // Ensure coupon form submission works properly
  $(document).on('submit', 'form.checkout_coupon', function(e) {
    e.preventDefault();
    var form = $(this);
    var coupon_code = form.find('input[name="coupon_code"]').val();

    if (coupon_code) {
      // Use WooCommerce's built-in AJAX for coupon application
      $.ajax({
        type: 'POST',
        url: wc_checkout_params.ajax_url,
        data: {
          action: 'woocommerce_apply_coupon',
          security: wc_checkout_params.apply_coupon_nonce,
          coupon_code: coupon_code
        },
        success: function(response) {
          if (response.success) {
            // Trigger checkout update
            $('body').trigger('update_checkout');
          } else {
            // Show error message
            $('.woocommerce-error, .woocommerce-message').remove();
            form.before('<div class="woocommerce-error">' + response.data + '</div>');
          }
        }
      });
    }
    return false;
  });

});