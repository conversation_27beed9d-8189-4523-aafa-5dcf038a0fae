#!/bin/bash
# change-domain-to-local.sh - Change WordPress URLs from pinkangel.bg to pinkangel.local
# This script is specifically designed for changing production database URLs to local development URLs

set -e

# Configuration
SOURCE_URL="https://pinkangel.bg"
TARGET_URL="https://pinkangel.local"
SOURCE_DOMAIN="pinkangel.bg"
TARGET_DOMAIN="pinkangel.local"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }

# Function to show usage
show_usage() {
    echo "Usage: $0 [environment]"
    echo ""
    echo "Change WordPress URLs from pinkangel.bg to pinkangel.local"
    echo "This script is designed for use after importing production database"
    echo ""
    echo "Arguments:"
    echo "  environment    Target environment (dev|prod|auto)"
    echo "                 auto = detect running environment (default)"
    echo ""
    echo "Examples:"
    echo "  $0              # Auto-detect environment"
    echo "  $0 dev          # Change URLs in development environment"
    echo "  $0 prod         # Change URLs in production environment"
    echo ""
    echo "Prerequisites:"
    echo "  - WordPress environment must be running"
    echo "  - Database should contain pinkangel.bg URLs"
    echo "  - /etc/hosts should contain: 127.0.0.1 pinkangel.local"
}

# Function to detect environment
detect_environment() {
    if docker ps --format "table {{.Names}}" | grep -q "wordpress-dev"; then
        echo "dev"
    elif docker ps --format "table {{.Names}}" | grep -q "wordpress-prod"; then
        echo "prod"
    else
        echo "none"
    fi
}

# Function to check prerequisites
check_prerequisites() {
    print_step "Checking prerequisites..."
    
    # Check if /etc/hosts contains pinkangel.local
    if ! grep -q "pinkangel.local" /etc/hosts 2>/dev/null; then
        print_warning "/etc/hosts does not contain pinkangel.local entry"
        print_info "To add it, run: echo '127.0.0.1 pinkangel.local' | sudo tee -a /etc/hosts"
        echo ""
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_error "Aborted by user"
            exit 1
        fi
    else
        print_success "/etc/hosts contains pinkangel.local entry"
    fi
    
    # Check if reverse proxy is running (for production)
    if [ "$1" = "prod" ]; then
        if ! docker ps --format "table {{.Names}}" | grep -q "nginx-proxy"; then
            print_warning "nginx-proxy is not running"
            print_info "For full functionality, start the reverse proxy: ./scripts/manage-proxy.sh start"
            echo ""
        else
            print_success "nginx-proxy is running"
        fi
    fi
}

# Function to change URLs
change_urls() {
    local container="$1"
    local environment="$2"
    
    print_step "Changing URLs in $environment environment..."
    print_info "Container: $container"
    print_info "Source URL: $SOURCE_URL"
    print_info "Target URL: $TARGET_URL"
    echo ""
    
    # Check if container is running
    if ! docker ps --format "table {{.Names}}" | grep -q "$container"; then
        print_error "$environment environment not running!"
        case $environment in
            development)
                print_error "Please start it first: make dev"
                ;;
            production)
                print_error "Please start it first: make prod"
                ;;
        esac
        return 1
    fi
    
    # Show current URLs
    print_info "Current URLs in database:"
    local current_site=$(docker exec $container wp option get siteurl --allow-root 2>/dev/null || echo "unknown")
    local current_home=$(docker exec $container wp option get home --allow-root 2>/dev/null || echo "unknown")
    echo "  Site URL: $current_site"
    echo "  Home URL: $current_home"
    echo ""
    
    # Check if URLs need changing
    if [[ "$current_site" == *"pinkangel.local"* ]] && [[ "$current_home" == *"pinkangel.local"* ]]; then
        print_warning "URLs already appear to be set to pinkangel.local"
        echo ""
        read -p "Continue with URL replacement anyway? (y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Skipping URL changes"
            return 0
        fi
    fi
    
    # Update WordPress core URLs
    print_step "Updating WordPress core URLs..."
    docker exec $container wp option update siteurl "$TARGET_URL" --allow-root
    docker exec $container wp option update home "$TARGET_URL" --allow-root
    print_success "Core URLs updated"
    echo ""
    
    # Search and replace URLs in content (dry run first)
    print_step "Analyzing URL replacements needed..."
    print_info "Running dry-run to show what will be changed..."
    echo ""
    
    # Dry run for HTTPS URLs
    local https_count=$(docker exec $container wp search-replace "$SOURCE_URL" "$TARGET_URL" --allow-root --dry-run 2>/dev/null | grep -o '[0-9]\+ replacements' | grep -o '[0-9]\+' || echo "0")
    print_info "HTTPS URLs to replace: $https_count"
    
    # Dry run for HTTP URLs (in case there are any)
    local http_source="http://$SOURCE_DOMAIN"
    local http_target="https://$TARGET_DOMAIN"
    local http_count=$(docker exec $container wp search-replace "$http_source" "$http_target" --allow-root --dry-run 2>/dev/null | grep -o '[0-9]\+ replacements' | grep -o '[0-9]\+' || echo "0")
    print_info "HTTP URLs to replace: $http_count"
    
    # Dry run for domain-only replacements
    local domain_count=$(docker exec $container wp search-replace "$SOURCE_DOMAIN" "$TARGET_DOMAIN" --allow-root --dry-run 2>/dev/null | grep -o '[0-9]\+ replacements' | grep -o '[0-9]\+' || echo "0")
    print_info "Domain-only references to replace: $domain_count"
    
    local total_replacements=$((https_count + http_count + domain_count))
    echo ""
    print_info "Total replacements needed: $total_replacements"
    
    if [ "$total_replacements" -eq 0 ]; then
        print_warning "No URL replacements needed"
    else
        echo ""
        read -p "Apply these URL replacements? (y/N): " -n 1 -r
        echo ""
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_step "Applying URL replacements..."
            
            # Apply HTTPS URL replacements
            if [ "$https_count" -gt 0 ]; then
                print_info "Replacing HTTPS URLs..."
                docker exec $container wp search-replace "$SOURCE_URL" "$TARGET_URL" --allow-root
            fi
            
            # Apply HTTP URL replacements
            if [ "$http_count" -gt 0 ]; then
                print_info "Replacing HTTP URLs..."
                docker exec $container wp search-replace "$http_source" "$http_target" --allow-root
            fi
            
            # Apply domain-only replacements
            if [ "$domain_count" -gt 0 ]; then
                print_info "Replacing domain references..."
                docker exec $container wp search-replace "$SOURCE_DOMAIN" "$TARGET_DOMAIN" --allow-root
            fi
            
            print_success "URL replacements completed!"
        else
            print_warning "URL replacement skipped"
        fi
    fi
    
    echo ""
    
    # Clear caches
    print_step "Clearing caches..."
    docker exec $container wp cache flush --allow-root 2>/dev/null && print_info "WordPress cache cleared" || print_warning "Could not clear WordPress cache"
    docker exec $container wp redis flush --allow-root 2>/dev/null && print_info "Redis cache cleared" || print_warning "Could not clear Redis cache"
    
    # Clear any object cache
    docker exec $container wp eval 'if (function_exists("wp_cache_flush")) { wp_cache_flush(); echo "Object cache cleared\n"; }' --allow-root 2>/dev/null || true
    
    print_success "Cache clearing completed"
    echo ""
    
    # Verify URLs
    print_step "Verifying URL changes..."
    local new_site=$(docker exec $container wp option get siteurl --allow-root 2>/dev/null || echo "unknown")
    local new_home=$(docker exec $container wp option get home --allow-root 2>/dev/null || echo "unknown")
    echo "  Site URL: $new_site"
    echo "  Home URL: $new_home"
    
    # Check if URLs were changed successfully
    if [[ "$new_site" == "$TARGET_URL" ]] && [[ "$new_home" == "$TARGET_URL" ]]; then
        print_success "URLs successfully changed to pinkangel.local!"
    else
        print_warning "URLs may not have been changed correctly"
        print_warning "Expected: $TARGET_URL"
        print_warning "Got Site: $new_site"
        print_warning "Got Home: $new_home"
    fi
}

# Main execution
echo "🌐 WordPress Domain Changer: pinkangel.bg → pinkangel.local"
echo "============================================================"
echo ""

# Parse arguments
ENVIRONMENT="${1:-auto}"

case $ENVIRONMENT in
    -h|--help)
        show_usage
        exit 0
        ;;
    dev|development)
        ENVIRONMENT="dev"
        ;;
    prod|production)
        ENVIRONMENT="prod"
        ;;
    auto)
        ENVIRONMENT=$(detect_environment)
        ;;
    *)
        print_error "Invalid environment: $ENVIRONMENT"
        show_usage
        exit 1
        ;;
esac

# Check detected environment
if [ "$ENVIRONMENT" = "none" ]; then
    print_error "No WordPress environment detected!"
    print_error "Please start either development or production environment first:"
    echo "  make dev   # For development"
    echo "  make prod  # For production"
    exit 1
fi

print_info "Detected environment: $ENVIRONMENT"
echo ""

# Check prerequisites
check_prerequisites "$ENVIRONMENT"
echo ""

# Change URLs based on environment
case $ENVIRONMENT in
    dev)
        change_urls "wordpress-dev" "development"
        ;;
    prod)
        change_urls "wordpress-prod" "production"
        ;;
    *)
        print_error "Unknown environment: $ENVIRONMENT"
        exit 1
        ;;
esac

echo ""
print_success "Domain change completed!"
echo ""
print_info "Next steps:"
case $ENVIRONMENT in
    dev)
        echo "1. Test direct access: http://localhost:8080"
        echo "2. Test via proxy: https://pinkangel.local (if proxy is running)"
        echo "3. Check admin: http://localhost:8080/wp-admin"
        ;;
    prod)
        echo "1. Start reverse proxy if not running: ./scripts/manage-proxy.sh start"
        echo "2. Test your site: https://pinkangel.local"
        echo "3. Check admin: https://pinkangel.local/wp-admin"
        ;;
esac
echo "4. Clear your browser cache if you see any redirects"
echo "5. If you encounter SSL warnings, accept them (self-signed certificates)"

print_success "Done!"
